import 'package:flutter/material.dart';

/// 字幕样式配置模型
class SubtitleStyle {
  final int padding;
  final double height;
  final double fontSize;
  final FontWeight fontWeight;
  final int shadowWidth;

  const SubtitleStyle({
    this.padding = 8,
    this.height = 1.4,
    this.fontSize = 50.0,
    this.fontWeight = FontWeight.normal,
    this.shadowWidth = 8,
  });

  SubtitleStyle copyWith({
    int? padding,
    double? height,
    double? fontSize,
    FontWeight? fontWeight,
    int? shadowWidth,
  }) {
    return SubtitleStyle(
      padding: padding ?? this.padding,
      height: height ?? this.height,
      fontSize: fontSize ?? this.fontSize,
      fontWeight: fontWeight ?? this.fontWeight,
      shadowWidth: shadowWidth ?? this.shadowWidth,
    );
  }

  TextStyle getTextStyle() {
    return TextStyle(
      color: Colors.white,
      backgroundColor: Colors.transparent,
      height: height,
      fontSize: fontSize,
      fontWeight: fontWeight,
      shadows: [
        Shadow(color: Colors.black, blurRadius: shadowWidth.toDouble()),
      ],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'padding': padding,
      'height': height,
      'fontSize': fontSize,
      'fontWeight': fontWeight.index,
      'shadowWidth': shadowWidth,
    };
  }

  factory SubtitleStyle.fromJson(Map<String, dynamic> json) {
    return SubtitleStyle(
      padding: json['padding'] as int,
      height: (json['height'] as num).toDouble(),
      fontSize: (json['fontSize'] as num).toDouble(),
      fontWeight: FontWeight.values[json['fontWeight'] as int],
      shadowWidth: json['shadowWidth'] as int,
    );
  }
}
