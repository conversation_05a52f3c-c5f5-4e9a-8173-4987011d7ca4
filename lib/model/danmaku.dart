import 'dart:convert';

import 'package:canvas_danmaku/canvas_danmaku.dart';
import 'package:flutter/material.dart';

/// 弹幕数据模型
class Danmaku {
  /// 弹幕文本
  final String text;

  /// 出现时间
  final Duration time;

  /// 弹幕类型 弹幕模式：1-普通弹幕，4-底部弹幕，5-顶部弹幕
  final int type;

  /// 弹幕颜色 32位整数表示的颜色，算法为 Rx256x256+Gx256+B，R/G/B的范围应是0-255
  final Color color;

  final String source;

  final String userid;

  const Dan<PERSON><PERSON>({
    required this.text,
    required this.time,
    required this.type,
    required this.color,
    required this.source,
    required this.userid,
  });

  // 格式: 出现时间,模式,颜色,[平台名]用户ID
  // 30.82,1,25,16777215,[5dm]379579
  // [平台名]可能为空
  factory Danmaku.fromJson(String p, String m) {
    final parts = p.split(',');
    final time = Duration(
      milliseconds: (double.parse(parts[0]) * 1000).round(),
    );
    final type = int.parse(parts[1]);
    final colorValue = int.parse(parts[2]);
    final color = Color.fromRGBO(
      colorValue ~/ 256 ~/ 256,
      colorValue ~/ 256 % 256,
      colorValue % 256,
      1.0,
    );
    // 解析用户ID和平台名
    String userPart = parts[3];
    String? source;
    String userId;

    // 检查是否有平台名
    RegExp sourceRegex = RegExp(r'^\[(.+)\](.+)$');
    Match? match = sourceRegex.firstMatch(userPart);

    if (match != null) {
      source = match.group(1);
      userId = match.group(2)!;
    } else {
      source = null;
      userId = userPart;
    }

    return Danmaku(
      text: m,
      time: time,
      type: type,
      color: color,
      source: source ?? 'DanDanPlay',
      userid: userId,
    );
  }

  Map<String, dynamic> toJson() {
    final colorValue =
        (color.r * 255 * 256 * 256).round() +
        (color.g * 255 * 256).round() +
        (color.b * 255).round();
    final p =
        "${time.inMilliseconds / 1000},$type,$colorValue,[$source]$userid";
    return {'p': p, 'm': text};
  }
}

class Anime {
  final String animeTitle;
  final int animeId;
  final List<Episode> episodes;

  const Anime({
    required this.animeTitle,
    required this.animeId,
    required this.episodes,
  });

  factory Anime.fromJson(Map<String, dynamic> json) {
    return Anime(
      animeTitle: json['animeTitle'] as String,
      animeId: json['animeId'] as int,
      episodes:
          (json['episodes'] as List)
              .map(
                (e) => Episode.fromSearchJson(
                  e as Map<String, dynamic>,
                  json['animeTitle'] as String,
                  json['animeId'] as int,
                ),
              )
              .toList(),
    );
  }
}

/// 弹弹play API 节目信息
class Episode {
  /// 节目ID
  final int episodeId;

  /// 动画名称
  final String animeTitle;

  /// 节目标题
  final String episodeTitle;

  /// 番剧ID
  final int animeId;

  const Episode({
    required this.episodeId,
    required this.animeId,
    required this.animeTitle,
    required this.episodeTitle,
  });

  /// 从JSON创建节目信息
  factory Episode.fromJson(Map<String, dynamic> json) {
    return Episode(
      episodeId: json['episodeId'] as int,
      animeTitle: json['animeTitle'] as String,
      episodeTitle: json['episodeTitle'] as String,
      animeId: json['animeId'] as int,
    );
  }

  /// 从搜索API结果创建节目信息
  factory Episode.fromSearchJson(
    Map<String, dynamic> episodeJson,
    String animeTitle,
    int animeId,
  ) {
    return Episode(
      episodeId: episodeJson['episodeId'] as int,
      animeTitle: animeTitle,
      episodeTitle: episodeJson['episodeTitle'] as String,
      animeId: animeId,
    );
  }
}

/// 弹弹play API 弹幕评论
class DanmakuComment {
  /// 弹幕ID
  final int cid;

  /// 弹幕参数
  final String p;

  /// 弹幕内容
  final String m;

  const DanmakuComment({required this.cid, required this.p, required this.m});

  /// 从JSON创建弹幕评论
  factory DanmakuComment.fromJson(Map<String, dynamic> json) {
    return DanmakuComment(
      cid: json['cid'] as int,
      p: json['p'] as String,
      m: json['m'] as String,
    );
  }

  /// 转换为Danmaku对象
  Danmaku toDanmaku() {
    return Danmaku.fromJson(p, m);
  }
}

// 弹幕设置
class DanmakuSettings {
  // 描边宽度
  double strokeWidth;
  // 透明度
  double opacity;
  // 显示时长
  int duration;
  // 字体大小
  double fontSize;
  // 弹幕区域
  double danmakuArea;
  // 隐藏顶部弹幕
  bool hideTop;
  // 隐藏底部弹幕
  bool hideBottom;
  // 隐藏滚动弹幕
  bool hideScroll;
  // 显示大弹幕
  bool massiveMode;
  // 显示特殊弹幕
  bool hideSpecial;
  // 哔哩哔哩源
  bool bilibiliSource;
  // Gamer源
  bool gamerSource;
  // 弹弹play源
  bool dandanSource;
  // other源
  bool otherSource;
  // 字体粗细
  int danmakuFontWeight;

  DanmakuSettings({
    this.strokeWidth = 1.5,
    this.opacity = 1.0,
    this.duration = 8,
    this.fontSize = 16.0,
    this.danmakuArea = 1.0,
    this.hideTop = false,
    this.hideBottom = true,
    this.hideScroll = false,
    this.massiveMode = false,
    this.hideSpecial = false,
    this.bilibiliSource = true,
    this.gamerSource = true,
    this.dandanSource = true,
    this.otherSource = true,
    this.danmakuFontWeight = 4,
  });

  DanmakuOption toDanmakuOption() {
    return DanmakuOption(
      fontSize: fontSize,
      fontWeight: danmakuFontWeight,
      area: danmakuArea,
      duration: duration,
      opacity: opacity,
      hideBottom: hideBottom,
      hideScroll: hideScroll,
      hideTop: hideTop,
      hideSpecial: hideSpecial,
      strokeWidth: strokeWidth,
      massiveMode: massiveMode,
      safeArea: true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'strokeWidth': strokeWidth,
      'opacity': opacity,
      'duration': duration,
      'fontSize': fontSize,
      'danmakuArea': danmakuArea,
      'hideTop': hideTop,
      'hideBottom': hideBottom,
      'hideScroll': hideScroll,
      'massiveMode': massiveMode,
      'hideSpecial': hideSpecial,
      'danmakuFontWeight': danmakuFontWeight,
    };
  }

  factory DanmakuSettings.fromJson(Map<String, dynamic> json) {
    return DanmakuSettings(
      strokeWidth: json['strokeWidth'] as double,
      opacity: json['opacity'] as double,
      duration: json['duration'] as int,
      fontSize: json['fontSize'] as double,
      danmakuArea: json['danmakuArea'] as double,
      hideTop: json['hideTop'] as bool,
      hideBottom: json['hideBottom'] as bool,
      hideScroll: json['hideScroll'] as bool,
      massiveMode: json['massiveMode'] as bool,
      hideSpecial: json['hideSpecial'] as bool,
      danmakuFontWeight: json['danmakuFontWeight'] as int,
    );
  }

  // 默认json
  static String defaultJson() {
    return jsonEncode(DanmakuSettings().toJson());
  }

  DanmakuSettings copyWith({
    double? strokeWidth,
    double? opacity,
    int? duration,
    double? fontSize,
    double? danmakuArea,
    bool? hideTop,
    bool? hideBottom,
    bool? hideScroll,
    bool? massiveMode,
    bool? hideSpecial,
    int? danmakuFontWeight,
    bool? bilibiliSource,
    bool? gamerSource,
    bool? dandanSource,
    bool? otherSource,
  }) {
    return DanmakuSettings(
      strokeWidth: strokeWidth ?? this.strokeWidth,
      opacity: opacity ?? this.opacity,
      duration: duration ?? this.duration,
      fontSize: fontSize ?? this.fontSize,
      danmakuArea: danmakuArea ?? this.danmakuArea,
      hideTop: hideTop ?? this.hideTop,
      hideBottom: hideBottom ?? this.hideBottom,
      hideScroll: hideScroll ?? this.hideScroll,
      massiveMode: massiveMode ?? this.massiveMode,
      hideSpecial: hideSpecial ?? this.hideSpecial,
      danmakuFontWeight: danmakuFontWeight ?? this.danmakuFontWeight,
      bilibiliSource: bilibiliSource ?? this.bilibiliSource,
      gamerSource: gamerSource ?? this.gamerSource,
      dandanSource: dandanSource ?? this.dandanSource,
      otherSource: otherSource ?? this.otherSource,
    );
  }
}

class DanmakuFile {
  String videoPath;
  DateTime cacheTime;
  DateTime expireTime;
  List<Danmaku> danmakus;
  int episodeId;
  int animeId;

  DanmakuFile({
    required this.videoPath,
    required this.cacheTime,
    required this.expireTime,
    required this.danmakus,
    required this.episodeId,
    required this.animeId,
  });

  // 将对象转换为Map，用于JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'videoPath': videoPath,
      'cacheTime': cacheTime.millisecondsSinceEpoch,
      'expireTime': expireTime.millisecondsSinceEpoch,
      'danmakus': danmakus.map((d) => d.toJson()).toList(),
      'episodeId': episodeId,
      'animeId': animeId,
    };
  }

  // 从Map创建对象，用于JSON反序列化
  factory DanmakuFile.fromJson(Map<String, dynamic> json) {
    return DanmakuFile(
      videoPath: json['videoPath'],
      cacheTime: DateTime.fromMillisecondsSinceEpoch(json['cacheTime']),
      expireTime: DateTime.fromMillisecondsSinceEpoch(json['expireTime']),
      danmakus:
          (json['danmakus'] as List)
              .map(
                (item) =>
                    Danmaku.fromJson(item['p'] as String, item['m'] as String),
              )
              .toList(),
      episodeId: json['episodeId'] as int,
      animeId: json['animeId'] as int,
    );
  }

  // 将对象转换为JSON字符串
  String toJsonString() {
    return jsonEncode(toJson());
  }

  // 从JSON字符串创建对象
  factory DanmakuFile.fromJsonString(String jsonString) {
    return DanmakuFile.fromJson(jsonDecode(jsonString));
  }
}
