import 'dart:convert';
import 'package:dandanplay_flutter/service/storage.dart';

/// WebDAV同步状态枚举
enum SyncStatus {
  idle, // 空闲状态
  syncing, // 同步中
  success, // 同步成功
  failed, // 同步失败
  conflict, // 有冲突需要处理
}

/// WebDAV同步历史记录数据模型
class SyncHistoryData {
  final int lastSyncTime;
  final int version;
  final List<History> histories;

  const SyncHistoryData({
    required this.lastSyncTime,
    required this.version,
    required this.histories,
  });

  /// 从JSON创建对象
  factory SyncHistoryData.fromJson(Map<String, dynamic> json) {
    return SyncHistoryData(
      lastSyncTime: json['lastSyncTime'] as int,
      version: json['version'] as int? ?? 1,
      histories:
          (json['histories'] as List<dynamic>)
              .map((e) => History.fromJson(e as Map<String, dynamic>))
              .toList(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'lastSyncTime': lastSyncTime,
      'version': version,
      'histories': histories.map((e) => e.toJson()).toList(),
    };
  }

  /// 从JSON字符串创建对象
  factory SyncHistoryData.fromJsonString(String jsonString) {
    final json = jsonDecode(jsonString) as Map<String, dynamic>;
    return SyncHistoryData.fromJson(json);
  }

  /// 转换为JSON字符串
  String toJsonString() {
    return jsonEncode(toJson());
  }

  /// 创建副本
  SyncHistoryData copyWith({
    String? deviceId,
    String? deviceName,
    int? lastSyncTime,
    int? version,
    List<History>? histories,
  }) {
    return SyncHistoryData(
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      version: version ?? this.version,
      histories: histories ?? this.histories,
    );
  }
}

/// 同步结果模型
class SyncResult {
  final bool success;
  final String? error;
  final int syncedCount;
  final int conflictCount;
  final DateTime syncTime;

  const SyncResult({
    required this.success,
    this.error,
    required this.syncedCount,
    required this.conflictCount,
    required this.syncTime,
  });

  factory SyncResult.success({
    required int syncedCount,
    required int conflictCount,
  }) {
    return SyncResult(
      success: true,
      syncedCount: syncedCount,
      conflictCount: conflictCount,
      syncTime: DateTime.now(),
    );
  }

  factory SyncResult.failure(String error) {
    return SyncResult(
      success: false,
      error: error,
      syncedCount: 0,
      conflictCount: 0,
      syncTime: DateTime.now(),
    );
  }

  @override
  String toString() {
    if (success) {
      return 'SyncResult(success: true, synced: $syncedCount, conflicts: $conflictCount)';
    } else {
      return 'SyncResult(success: false, error: $error)';
    }
  }
}
