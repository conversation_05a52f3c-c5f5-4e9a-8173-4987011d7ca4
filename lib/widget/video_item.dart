import 'dart:io';

import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/utils/format.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:path_provider/path_provider.dart';

class VideoItem extends StatelessWidget with FItemMixin {
  final History? history;
  final String name;
  final void Function() onPress;
  final void Function()? onLongPress;
  VideoItem({
    super.key,
    required this.history,
    required this.name,
    required this.onPress,
    this.onLongPress,
  });

  Future<Widget> _buildPerfix(History? history) async {
    if (history != null) {
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().second;
      return ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: Image.file(
          File('${directory.path}/screenshots/${history.uniqueKey}'),
          fit: BoxFit.fitHeight,
          key: ValueKey('${history.uniqueKey}$timestamp'),
          errorBuilder: (context, error, stackTrace) {
            return const Icon(FIcons.play, size: 50);
          },
        ),
      );
    }
    return const Icon(FIcons.play, size: 50);
  }

  @override
  Widget build(BuildContext context) {
    double progress = 0;
    int progressPercent = 0;
    String lastWatchTime = '';
    if (history != null) {
      progress =
          history!.duration > 0
              ? (history!.position / history!.duration).clamp(0.0, 1.0)
              : 0.0;
      progressPercent = (progress * 100).round();
      lastWatchTime = formatLastWatchTime(history!.updateTime);
    }
    final subtitleStyle = context.theme.itemStyle.contentStyle.subtitleTextStyle
        .resolve({});
    return FItem(
      prefix: SizedBox(
        width: 90,
        height: 70,
        child: FutureBuilder(
          future: _buildPerfix(history),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const CircularProgressIndicator();
            }
            if (snapshot.hasData) {
              return snapshot.data!;
            }
            return const Icon(FIcons.file, size: 50);
          },
        ),
      ),
      title: ConstrainedBox(
        constraints: const BoxConstraints(minHeight: 70),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(name, style: context.theme.typography.base, maxLines: 2),
            history != null
                ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            style: subtitleStyle,
                            '观看进度: ${formatTime(history!.position, history!.duration)}',
                          ),
                        ),
                        Text(
                          style: subtitleStyle,
                          '$progressPercent%',
                          // style: TextStyle(
                          //   color: context.theme.colors.mutedForeground,
                          //   fontSize: 12,
                          // ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    FProgress(
                      value: progress,
                      duration: Duration.zero,
                      style:
                          (style) => style.copyWith(
                            constraints: style.constraints.copyWith(
                              minHeight: 4,
                              maxHeight: 4,
                            ),
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(style: subtitleStyle, '最后观看: $lastWatchTime'),
                  ],
                )
                : Text(style: subtitleStyle, '未观看'),
          ],
        ),
      ),
      onPress: onPress,
      onLongPress: onLongPress,
    );
  }
}
