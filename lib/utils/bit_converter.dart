import 'dart:math';

class BitConverter {
  static const _units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  /// Converts bits to a human-readable format.
  ///
  /// [bits] The number of bits to convert.
  /// [decimals] The number of decimal places to include in the output.
  static String format(int bits, [int decimals = 2]) {
    if (bits <= 0) return '0B';
    var i = (log(bits) / log(1024)).floor();
    return '${(bits / pow(1024, i)).toStringAsFixed(decimals)}${_units[i]}';
  }
}
