/// 视频播放器工具类
class VideoPlayerUtils {
  /// 检查是否为支持的视频格式
  static bool isSupportedVideoFormat(String path) {
    final supportedExtensions = [
      '.mp4',
      '.avi',
      '.mkv',
      '.mov',
      '.wmv',
      '.flv',
      '.webm',
      '.m4v',
      '.3gp',
      '.ts',
      '.m3u8',
    ];

    final lowerPath = path.toLowerCase();
    return supportedExtensions.any((ext) => lowerPath.endsWith(ext));
  }

  /// 检查是否为网络视频URL
  static bool isNetworkVideo(String path) {
    return path.startsWith('http://') || path.startsWith('https://');
  }

  /// 从文件路径提取文件名作为标题
  static String extractTitleFromPath(String path) {
    if (isNetworkVideo(path)) {
      try {
        final uri = Uri.parse(path);
        final segments = uri.pathSegments;
        if (segments.isNotEmpty) {
          return segments.last;
        }
      } catch (e) {
        // 如果解析失败，返回默认标题
      }
      return '网络视频';
    } else {
      // 本地文件
      final parts = path.split('/');
      if (parts.isNotEmpty && parts.last.isNotEmpty) {
        return parts.last;
      }
      return '本地视频';
    }
  }
}
