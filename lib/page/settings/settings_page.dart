import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(scrolledUnderElevation: 0, title: const Text('设置')),
      body: FItemGroup(
        divider: FItemDivider.indented,
        children: [
          FItem(
            prefix: const Icon(FIcons.video, size: 24),
            title: const Text('播放器设置'),
            subtitle: const Text('设置播放器的相关参数'),
            onPress: () => context.push('/settings/player'),
          ),
          FItem(
            prefix: const Icon(FIcons.palette, size: 24),
            title: const Text('外观设置'),
            subtitle: const Text('设置应用主题和刷新率'),
            onPress: () => context.push('/settings/appearance'),
          ),
          FItem(
            prefix: const Icon(FIcons.cloud, size: 24),
            title: const Text('同步设置'),
            subtitle: const Text('设置 WebDAV 同步参数'),
            onPress: () => context.push('/settings/webdav'),
          ),
        ],
      ),
    );
  }
}
