import 'package:dandanplay_flutter/router.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:signals_flutter/signals_flutter.dart';

class RootPage extends StatefulWidget {
  const RootPage({super.key});

  @override
  State<RootPage> createState() => RootPageState();
}

class RootPageState extends State<RootPage> with TickerProviderStateMixin {
  final mediaLibraryService = GetIt.I.get<MediaLibraryService>();

  void _showDeleteDialog(MediaLibrary library) {
    showAdaptiveDialog(
      context: context,
      builder:
          (context) => FDialog(
            direction: Axis.vertical,
            title: const Text('删除媒体库'),
            body: Text('确定要删除媒体库 "${library.name}" 吗？'),
            actions: [
              FButton(
                onPress: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              FButton(
                style: FButtonStyle.destructive(),
                onPress: () {
                  Navigator.pop(context);
                  mediaLibraryService.deleteMediaLibrary(library.id);
                },
                child: const Text('删除'),
              ),
            ],
          ),
    );
  }

  Future<void> _playLocalVideo() async {
    try {
      // 使用 file_picker 选择视频文件
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final filePath = result.files.single.path!;

        // 设置 GlobalPlayerService
        final globalPlayerService = GetIt.I.get<GlobalPlayerService>();
        globalPlayerService.currentVideoPath = filePath;
        globalPlayerService.virtualVideoPath = filePath;
        globalPlayerService.historiesType = HistoriesType.local;
        globalPlayerService.mediaLibraryId = null;

        // 导航到播放器
        if (mounted) {
          final location = Uri(path: videoPlayerPath);
          context.push(location.toString());
        }
      }
    } catch (e) {
      // 显示错误信息
      if (mounted) {
        showFToast(
          context: context,
          title: const Text('选择文件失败'),
          description: Text('$e'),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final controller = FPopoverController(vsync: this);
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.transparent,
        ),
        scrolledUnderElevation: 0,
        title: Text(
          '媒体库',
          style: context.theme.typography.xl2.copyWith(height: 1.2),
        ),
        actions: [
          FButton.icon(
            style: FButtonStyle.ghost(),
            child: const Icon(FIcons.settings, size: 24),
            onPress: () => context.push(settingsPath),
          ),
          FPopoverMenu(
            popoverController: controller,
            menuAnchor: Alignment.topRight,
            childAnchor: Alignment.bottomRight,
            menu: [
              FItemGroup(
                children: [
                  FItem(
                    prefix: const Icon(FIcons.library),
                    title: const Text('添加媒体库'),
                    onPress: () {
                      controller.toggle();
                      context.push(editMediaLibraryPath);
                    },
                  ),
                  FItem(
                    prefix: const Icon(FIcons.clock),
                    title: const Text('观看历史'),
                    onPress: () {
                      controller.toggle();
                      context.push(historyPath);
                    },
                  ),
                  FItem(
                    prefix: const Icon(FIcons.play),
                    title: const Text('播放本地视频'),
                    onPress: () {
                      controller.toggle();
                      _playLocalVideo();
                    },
                  ),
                ],
              ),
            ],
            child: FButton.icon(
              style: FButtonStyle.ghost(),
              onPress: controller.toggle,
              child: const Icon(FIcons.ellipsis, size: 24),
            ),
          ),
          SizedBox(width: 6),
        ],
      ),
      body: Watch((_) {
        final libraries = mediaLibraryService.mediaLibraries.value;
        return FItemGroup(
          children:
              libraries
                  .map(
                    (library) => _PopoverMenu(
                      edit: () {
                        context.push('$editMediaLibraryPath?id=${library.id}');
                      },
                      delete: () => _showDeleteDialog(library),
                      child:
                          (controller) => FItem(
                            prefix: const Icon(FIcons.folder, size: 40),
                            title: Text(
                              library.name,
                              style: context.theme.typography.xl,
                            ),
                            subtitle: Text(library.url),
                            onPress: () {
                              context.push(
                                '$fileExplorerPath?id=${library.id}&name=${library.name}',
                              );
                            },
                            onLongPress: () async {
                              controller.toggle();
                            },
                          ),
                    ),
                  )
                  .toList(),
        );
      }),
    );
  }
}

class _PopoverMenu extends StatefulWidget with FItemMixin {
  final Function edit;
  final Function delete;
  final Widget Function(FPopoverController controller) child;
  const _PopoverMenu({
    required this.edit,
    required this.delete,
    required this.child,
  });
  @override
  _PopoverMenuState createState() => _PopoverMenuState();
}

class _PopoverMenuState extends State<_PopoverMenu>
    with TickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    final controller = FPopoverController(vsync: this);
    return FPopoverMenu(
      popoverController: controller,
      menu: [
        FItemGroup(
          children: [
            FItem(
              prefix: const Icon(FIcons.pencil),
              title: Text('编辑'),
              onPress: () {
                controller.toggle();
                widget.edit();
              },
            ),
            FItem(
              prefix: const Icon(FIcons.trash, color: Colors.red),
              title: Text('删除'),
              onPress: () {
                controller.toggle();
                widget.delete();
              },
            ),
          ],
        ),
      ],
      child: widget.child(controller),
    );
  }
}
