import 'package:dandanplay_flutter/service/configure.dart';
import 'package:dandanplay_flutter/widget/settings/settings_section.dart';
import 'package:dandanplay_flutter/widget/settings/settings_tile.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:signals_flutter/signals_flutter.dart';

/// 字幕样式设置面板
class SubtitleStyleSettingsPanel extends StatelessWidget {
  final VoidCallback? onClose;

  const SubtitleStyleSettingsPanel({super.key, this.onClose});

  @override
  Widget build(BuildContext context) {
    final configure = GetIt.I<ConfigureService>();
    return Scaffold(
      body: ListView(
        padding: EdgeInsets.all(4),
        children: [
          SettingsSection(
            title: '字体设置',
            children: [
              Watch((context) {
                final style = configure.subtitleStyle.value;
                return SettingsTile.sliderTile(
                  title: '字体大小',
                  details: style.fontSize.toStringAsFixed(0),
                  silderMin: 10,
                  silderMax: 100,
                  silderValue: style.fontSize,
                  silderDivisions: 45,
                  onSilderChange: (value) {
                    configure.subtitleStyle.value = style.copyWith(
                      fontSize: value,
                    );
                  },
                );
              }),
              Watch((context) {
                final style = configure.subtitleStyle.value;
                return SettingsTile.sliderTile(
                  title: '字体粗细',
                  onSilderChange: (value) {
                    configure.subtitleStyle.value = style.copyWith(
                      fontWeight: FontWeight.values[value.round()],
                    );
                  },
                  details: style.fontWeight.value.toString(),
                  silderMin: 0,
                  silderMax: 8,
                  silderValue: style.fontWeight.index.toDouble(),
                  silderDivisions: 8,
                );
              }),
              Watch((context) {
                final style = configure.subtitleStyle.value;
                return SettingsTile.sliderTile(
                  title: '描边宽度',
                  details: style.shadowWidth.toString(),
                  silderMin: 0,
                  silderMax: 20,
                  silderValue: style.shadowWidth.toDouble(),
                  silderDivisions: 10,
                  onSilderChange: (value) {
                    configure.subtitleStyle.value = style.copyWith(
                      shadowWidth: value.round(),
                    );
                  },
                );
              }),
              Watch((context) {
                final style = configure.subtitleStyle.value;
                return SettingsTile.sliderTile(
                  title: '行高',
                  details: style.height.toStringAsFixed(1),
                  silderMin: 0,
                  silderMax: 3,
                  silderValue: style.height,
                  silderDivisions: 30,
                  onSilderChange: (value) {
                    configure.subtitleStyle.value = style.copyWith(
                      height: value,
                    );
                  },
                );
              }),
            ],
          ),
          SettingsSection(
            title: '位置设置',
            children: [
              Watch((context) {
                final style = configure.subtitleStyle.value;
                return SettingsTile.sliderTile(
                  title: '底部边距',
                  details: style.padding.toString(),
                  silderMin: 0,
                  silderMax: 20,
                  silderValue: style.padding.toDouble(),
                  silderDivisions: 10,
                  onSilderChange: (value) {
                    configure.subtitleStyle.value = style.copyWith(
                      padding: value.round(),
                    );
                  },
                );
              }),
            ],
          ),
        ],
      ),
    );
  }
}
