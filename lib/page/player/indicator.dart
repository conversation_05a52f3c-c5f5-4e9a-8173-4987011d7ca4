import 'package:flutter/material.dart';
import 'package:flutter_volume_controller/flutter_volume_controller.dart';
import 'package:forui/forui.dart';
import 'package:screen_brightness/screen_brightness.dart';

enum IndicatorType { none, brightness, volume, speed }

/// 通用状态指示器
/// 用于显示亮度、音量、播放速度
class StatusIndicator extends StatefulWidget {
  final IndicatorType type;
  final double value;
  final bool isVisible;
  final Duration visibilityDuration;

  const StatusIndicator({
    super.key,
    required this.type,
    required this.value,
    required this.isVisible,
    this.visibilityDuration = const Duration(seconds: 2),
  });

  @override
  State<StatusIndicator> createState() => _StatusIndicatorState();
}

class _StatusIndicatorState extends State<StatusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    if (widget.isVisible) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(StatusIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(opacity: _fadeAnimation.value, child: _buildIndicator());
      },
    );
  }

  Widget _buildIndicator() {
    IconData icon;
    Widget child;

    switch (widget.type) {
      case IndicatorType.none:
        return const SizedBox.shrink();
      case IndicatorType.brightness:
        icon = _getBrightnessIcon(widget.value);
        child = _buildProgressIndicator(widget.value);
        break;
      case IndicatorType.volume:
        icon = _getVolumeIcon(widget.value);
        child = _buildProgressIndicator(widget.value);
        break;
      case IndicatorType.speed:
        icon = Icons.speed;
        child = Text('${widget.value}x', style: context.theme.typography.sm);
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: context.theme.colors.background,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [Icon(icon, size: 20), const SizedBox(width: 8), child],
      ),
    );
  }

  Widget _buildProgressIndicator(double value) {
    return SizedBox(
      width: 100,
      child: FProgress(
        duration: Duration.zero,
        style:
            (style) => style.copyWith(
              constraints: BoxConstraints(minHeight: 8, maxHeight: 8),
            ),
        value: value,
      ),
    );
  }

  IconData _getBrightnessIcon(double brightness) {
    if (brightness < 0.3) {
      return Icons.brightness_low;
    } else if (brightness < 0.7) {
      return Icons.brightness_medium;
    } else {
      return Icons.brightness_high;
    }
  }

  IconData _getVolumeIcon(double volume) {
    if (volume == 0) {
      return Icons.volume_off;
    } else if (volume < 0.5) {
      return Icons.volume_down;
    } else {
      return Icons.volume_up;
    }
  }
}

/// 亮度控制服务
class BrightnessVolumeService {
  static double _currentBrightness = 0.5;
  static double _systemBrightness = 0.5;

  /// 获取当前亮度
  static double get currentBrightness => _currentBrightness;

  /// 设置亮度
  static Future<void> setBrightness(double brightness) async {
    brightness = brightness.clamp(0.0, 1.0);
    _currentBrightness = brightness;

    try {
      await ScreenBrightness().setApplicationScreenBrightness(brightness);
    } catch (e) {
      debugPrint('设置亮度失败: $e');
    }
  }

  /// 重置为系统亮度
  static Future<void> resetToSystemBrightness() async {
    try {
      await ScreenBrightness().resetApplicationScreenBrightness();
      _currentBrightness = _systemBrightness;
    } catch (e) {
      debugPrint('重置亮度失败: $e');
    }
  }

  static Future<void> initialize() async {
    try {
      _systemBrightness = await ScreenBrightness().system;
      _currentBrightness = await ScreenBrightness().application;
      _currentVolume = await FlutterVolumeController.getVolume() ?? 0.5;

      FlutterVolumeController.addListener((volume) {
        _currentVolume = volume;
      });
    } catch (e) {
      debugPrint('初始化亮度音量服务失败: $e');
      _systemBrightness = 0.5;
      _currentBrightness = 0.5;
    }
  }

  static double _currentVolume = 0.5;

  /// 获取当前音量
  static double get currentVolume => _currentVolume;

  /// 设置音量
  static Future<void> setVolume(double volume) async {
    volume = volume.clamp(0.0, 1.0);
    _currentVolume = volume;

    try {
      await FlutterVolumeController.setVolume(volume);
    } catch (e) {
      debugPrint('设置音量失败: $e');
    }
  }

  /// 释放资源
  static void dispose() {
    resetToSystemBrightness();
    FlutterVolumeController.removeListener();
  }
}
