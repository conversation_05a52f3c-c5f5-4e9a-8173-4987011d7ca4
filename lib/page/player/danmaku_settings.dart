import 'package:dandanplay_flutter/service/player/danmaku.dart';
import 'package:dandanplay_flutter/widget/settings/settings_section.dart';
import 'package:dandanplay_flutter/widget/settings/settings_tile.dart';
import 'package:flutter/material.dart';
import 'package:signals_flutter/signals_flutter.dart';

/// 弹幕设置面板
class DanmakuSettingsPanel extends StatelessWidget {
  final VoidCallback? onClose;
  final DanmakuService danmakuService;

  const DanmakuSettingsPanel({
    super.key,
    this.onClose,
    required this.danmakuService,
  });

  @override
  Widget build(BuildContext context) {
    final settings = danmakuService.danmakuSettings.watch(context);
    return Scaffold(
      body: ListView(
        padding: EdgeInsets.all(4),
        children: [
          SettingsSection(
            title: '样式设置',
            children: [
              SettingsTile.sliderTile(
                title: '透明度',
                onSilderChange: (value) {
                  danmakuService.updateDanmakuSettings(
                    settings.copyWith(opacity: value),
                  );
                },
                details: settings.opacity.toStringAsFixed(1),
                silderValue: settings.opacity,
                silderDivisions: 10,
                silderMin: 0,
                silderMax: 1,
              ),
              SettingsTile.sliderTile(
                title: '字体大小',
                onSilderChange: (value) {
                  danmakuService.updateDanmakuSettings(
                    settings.copyWith(fontSize: value),
                  );
                },
                details: settings.fontSize.round().toString(),
                silderValue: settings.fontSize,
                silderDivisions: 22,
                silderMin: 10,
                silderMax: 32,
              ),
              SettingsTile.sliderTile(
                title: '字体粗细',
                onSilderChange: (value) {
                  danmakuService.updateDanmakuSettings(
                    settings.copyWith(danmakuFontWeight: value.round()),
                  );
                },
                details: settings.danmakuFontWeight.toString(),
                silderValue: settings.danmakuFontWeight.toDouble(),
                silderDivisions: 8,
                silderMin: 0,
                silderMax: 8,
              ),
              SettingsTile.sliderTile(
                title: '描边宽度',
                onSilderChange: (value) {
                  danmakuService.updateDanmakuSettings(
                    settings.copyWith(strokeWidth: value.ceilToDouble()),
                  );
                },
                details: settings.strokeWidth.toString(),
                silderValue: settings.strokeWidth,
                silderDivisions: 16,
                silderMin: 0,
                silderMax: 4,
              ),
              SettingsTile.sliderTile(
                title: '显示时长',
                onSilderChange: (value) {
                  danmakuService.updateDanmakuSettings(
                    settings.copyWith(duration: value.round()),
                  );
                },
                details: settings.duration.toString(),
                silderValue: settings.duration.toDouble(),
                silderDivisions: 16,
                silderMin: 1,
                silderMax: 17,
              ),
              SettingsTile.sliderTile(
                title: '弹幕区域',
                onSilderChange: (value) {
                  danmakuService.updateDanmakuSettings(
                    settings.copyWith(danmakuArea: value),
                  );
                },
                details: '${(settings.danmakuArea * 100).round()}%',
                silderValue: settings.danmakuArea,
                silderDivisions: 4,
                silderMin: 0,
                silderMax: 1,
              ),
            ],
          ),
          SettingsSection(
            title: '过滤设置',
            children: [
              SettingsTile.switchTile(
                title: '隐藏滚动弹幕',
                switchValue: settings.hideScroll,
                onBoolChange: (value) {
                  danmakuService.updateDanmakuSettings(
                    settings.copyWith(hideScroll: value),
                  );
                },
              ),
              SettingsTile.switchTile(
                title: '隐藏顶部弹幕',
                switchValue: settings.hideTop,
                onBoolChange: (value) {
                  danmakuService.updateDanmakuSettings(
                    settings.copyWith(hideTop: value),
                  );
                },
              ),
              SettingsTile.switchTile(
                title: '隐藏底部弹幕',
                switchValue: settings.hideBottom,
                onBoolChange: (value) {
                  danmakuService.updateDanmakuSettings(
                    settings.copyWith(hideBottom: value),
                  );
                },
              ),
              SettingsTile.switchTile(
                title: 'BiliBili弹幕',
                switchValue: settings.bilibiliSource,
                onBoolChange: (value) {
                  danmakuService.updateDanmakuSettings(
                    settings.copyWith(bilibiliSource: value),
                  );
                },
              ),
              SettingsTile.switchTile(
                title: 'Gamer弹幕',
                switchValue: settings.gamerSource,
                onBoolChange: (value) {
                  danmakuService.updateDanmakuSettings(
                    settings.copyWith(gamerSource: value),
                  );
                },
              ),
              SettingsTile.switchTile(
                title: 'DanDanPlay弹幕',
                switchValue: settings.dandanSource,
                onBoolChange: (value) {
                  danmakuService.updateDanmakuSettings(
                    settings.copyWith(dandanSource: value),
                  );
                },
              ),
              SettingsTile.switchTile(
                title: '其他弹幕',
                switchValue: settings.otherSource,
                onBoolChange: (value) {
                  danmakuService.updateDanmakuSettings(
                    settings.copyWith(otherSource: value),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
