import 'package:dandanplay_flutter/model/file_item.dart';
import 'package:dandanplay_flutter/page/player/subtitle_style_settings.dart';
import 'package:dandanplay_flutter/page/player/track_page.dart';
import 'package:dandanplay_flutter/page/player/danmaku_search_page.dart';
import 'package:dandanplay_flutter/page/player/danmaku_settings.dart';
import 'package:dandanplay_flutter/service/file_explorer.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:dandanplay_flutter/service/player/player.dart';
import 'package:dandanplay_flutter/utils/format.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:signals_flutter/signals_flutter.dart';

enum RightDrawerType {
  danmakuActions,
  danmakuSearch,
  danmakuSettings,
  episode,
  speed,
  audioTrack,
  subtitleTrack,
  subtitle,
}

class RightDrawerContent extends StatelessWidget {
  RightDrawerContent({
    super.key,
    required this.drawerType,
    required this.playerService,
    required this.onEpisodeSelected,
    required this.onDrawerChanged,
  });

  final RightDrawerType drawerType;
  final VideoPlayerService playerService;
  final void Function(int index) onEpisodeSelected;
  final void Function(RightDrawerType newType) onDrawerChanged;
  final _globalPlayerService = GetIt.I.get<GlobalPlayerService>();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(color: context.theme.colors.background),
      width: 300,
      height: MediaQuery.of(context).size.height,
      child: _buildContent(context),
    );
  }

  Widget _buildContent(BuildContext context) {
    switch (drawerType) {
      case RightDrawerType.speed:
        return _buildSpeedSettings(context);
      case RightDrawerType.danmakuActions:
        return _buildDanmakuActions(context);
      case RightDrawerType.danmakuSearch:
        return _buildDanmakuSearch(context);
      case RightDrawerType.danmakuSettings:
        return _buildDanmakuSettings(context);
      case RightDrawerType.episode:
        return _buildEpisodePanel(context);
      case RightDrawerType.audioTrack:
        return TrackPage(playerService: playerService, isAudio: true);
      case RightDrawerType.subtitleTrack:
        return TrackPage(playerService: playerService, isAudio: false);
      case RightDrawerType.subtitle:
        return SubtitleStyleSettingsPanel();
    }
  }

  Widget _buildSpeedSettings(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Watch((context) {
        final speed = playerService.playbackSpeed.watch(context);
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('播放速度: ${speed.toStringAsFixed(2)}x'),
            FSlider(
              controller: FDiscreteSliderController(
                allowedInteraction: FSliderInteraction.slideThumb,
                selection: FSliderSelection(max: (speed - 0.25) / 4),
              ),
              tooltipBuilder: (p0, p1) {
                return Text('${speed.toStringAsFixed(2)}x');
              },
              marks: [
                for (double i = 0; i <= 1.0; i += 0.0125)
                  FSliderMark(value: i, tick: false),
              ],
              onChange: (s) {
                playerService.setPlaybackSpeed(s.offset.max * 4 + 0.25);
              },
            ),
          ],
        );
      }),
    );
  }

  Widget _buildDanmakuActions(BuildContext context) {
    return Column(
      children: [
        FItemGroup(
          children: [
            FItem(
              prefix: const Icon(Icons.search, size: 20),
              title: Text('搜索弹幕', style: context.theme.typography.base),
              onPress: () => onDrawerChanged(RightDrawerType.danmakuSearch),
            ),
            FItem(
              prefix: const Icon(Icons.refresh, size: 20),
              title: Text('重新加载弹幕', style: context.theme.typography.base),
              onPress: () {
                Navigator.pop(context);
                _globalPlayerService.showNotification('正在重新加载弹幕...');
                playerService.danmakuService.loadDanmaku(force: true);
              },
            ),
            FItem(
              prefix: const Icon(Icons.settings_outlined, size: 20),
              title: Text('弹幕设置', style: context.theme.typography.base),
              onPress: () => onDrawerChanged(RightDrawerType.danmakuSettings),
            ),
            FItem(
              prefix: const Icon(Icons.audiotrack_outlined, size: 20),
              title: Text('音频轨道', style: context.theme.typography.base),
              onPress: () {
                onDrawerChanged(RightDrawerType.audioTrack);
              },
            ),
            FItem(
              prefix: const Icon(Icons.subtitles_outlined, size: 20),
              title: Text('字幕选择', style: context.theme.typography.base),
              onPress: () {
                onDrawerChanged(RightDrawerType.subtitleTrack);
              },
            ),
            FItem(
              prefix: const Icon(Icons.text_format_outlined, size: 20),
              title: Text('字幕样式', style: context.theme.typography.base),
              onPress: () {
                onDrawerChanged(RightDrawerType.subtitle);
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDanmakuSearch(BuildContext context) {
    return DanmakuSearchPage(
      searchEpisodes: (name) async {
        return playerService.danmakuService.searchEpisodes(name);
      },
      onEpisodeSelected: ({required animeId, required episodeId}) {
        Navigator.pop(context); // 关闭 sheet
        _globalPlayerService.showNotification('正在加载指定弹幕...');
        playerService.danmakuService.selectEpisodeAndLoadDanmaku(
          animeId,
          episodeId,
        );
      },
    );
  }

  Widget _buildDanmakuSettings(BuildContext context) {
    return DanmakuSettingsPanel(
      danmakuService: playerService.danmakuService,
      onClose: () {
        Navigator.of(context).pop();
      },
    );
  }

  final FileExplorerService fileExplorerService =
      GetIt.I.get<FileExplorerService>();
  List<FItemMixin> _buildItems(List<FileItem> files, BuildContext context) {
    final widgetList = <FItemMixin>[];
    for (var i = 0; i < files.length; i++) {
      final file = files[i];
      if (!file.isVideo) {
        continue;
      }
      widgetList.add(
        FItem(
          title: Text(file.name, maxLines: 2),
          subtitle:
              file.history != null
                  ? Text(
                    '观看进度: ${formatTime(file.history!.position, file.history!.duration)}',
                  )
                  : Text(''),
          onPress: () => {onEpisodeSelected(i), Navigator.pop(context)},
        ),
      );
    }
    return widgetList;
  }

  Widget _buildEpisodePanel(BuildContext context) {
    return Watch(
      (context) => fileExplorerService.files.value.map(
        data: (files) {
          if (files.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    FIcons.folder,
                    size: 48,
                    color: context.theme.colors.mutedForeground,
                  ),
                  const SizedBox(height: 16),
                  Text('播放列表为空', style: context.theme.typography.lg),
                ],
              ),
            );
          }
          return FItemGroup(children: _buildItems(files, context));
        },
        error: (error, stack) => const Center(child: Text('加载失败')),
        loading: () => const Center(child: CircularProgressIndicator()),
      ),
    );
  }
}
