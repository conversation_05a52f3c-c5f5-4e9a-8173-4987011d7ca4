import 'package:dandanplay_flutter/router.dart';
import 'package:dandanplay_flutter/service/file_explorer.dart';
import 'package:dandanplay_flutter/service/history.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/widget/video_item.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:path/path.dart' as path;
import 'package:signals_flutter/signals_flutter.dart';

class HistoryPage extends StatefulWidget {
  const HistoryPage({super.key});

  @override
  State<HistoryPage> createState() => _HistoryPageState();
}

class _HistoryPageState extends State<HistoryPage> {
  final HistoryService _historyService = GetIt.I.get<HistoryService>();
  final MediaLibraryService _mediaLibraryService =
      GetIt.I.get<MediaLibraryService>();
  final FileExplorerService _fileExplorerService =
      GetIt.I.get<FileExplorerService>();
  final GlobalPlayerService _globalPlayerService =
      GetIt.I.get<GlobalPlayerService>();

  final Signal<bool> _isLoading = signal(true);
  final Signal<bool> _isLoadingMore = signal(false);
  final Signal<String?> _error = signal(null);
  final Signal<List<History>> _histories = signal([]);
  final Signal<bool> _hasMoreData = signal(true);
  final ScrollController _scrollController = ScrollController();

  // 分页参数
  static const int _pageSize = 20;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _loadHistories();

    // 设置滚动监听器
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 滚动监听器
  void _onScroll() {
    if (!_scrollController.hasClients) return;
    final position = _scrollController.position;
    final maxScrollExtent = position.maxScrollExtent;
    final currentPixels = position.pixels;

    // 距离底部200像素时开始加载更多
    if (currentPixels >= maxScrollExtent - 200) {
      if (!_isLoadingMore.value && _hasMoreData.value && !_isLoading.value) {
        _loadMoreHistories();
      }
    }
  }

  Future<void> _loadHistories() async {
    _isLoading.value = true;
    _error.value = null;
    _currentPage = 0;
    _histories.value = [];

    try {
      final newHistories = await _historyService.getHistoriesWithPagination(
        0,
        _pageSize,
      );
      _histories.value = newHistories;
      _hasMoreData.value = newHistories.length == _pageSize;
      _currentPage = 1;
    } catch (e) {
      _error.value = '加载历史记录失败: $e';
    } finally {
      _isLoading.value = false;
    }
  }

  // 刷新历史记录（重置分页）
  Future<void> _refreshHistories() async {
    _currentPage = 0;
    _hasMoreData.value = true;
    await _loadHistories();
  }

  // 加载更多历史记录
  Future<void> _loadMoreHistories() async {
    if (_isLoadingMore.value || !_hasMoreData.value) return;

    _isLoadingMore.value = true;

    try {
      final newHistories = await _historyService.getHistoriesWithPagination(
        _currentPage,
        _pageSize,
      );
      if (newHistories.isNotEmpty) {
        _histories.value = [..._histories.value, ...newHistories];
        _currentPage++;
        _hasMoreData.value = newHistories.length == _pageSize;
      } else {
        _hasMoreData.value = false;
      }
    } catch (e) {
      _error.value = '加载更多历史记录失败: $e';
    } finally {
      _isLoadingMore.value = false;
    }
  }

  // 删除单个历史记录
  Future<void> _deleteHistory(History history) async {
    try {
      await _historyService.deleteHistory(history.id);
      _histories.value =
          _histories.value.where((h) => h.id != history.id).toList();
      // 如果删除后当前页面项目不足，尝试加载更多
      if (_histories.value.length < _pageSize && _hasMoreData.value) {
        _loadMoreHistories();
      }
    } catch (e) {
      _error.value = '删除历史记录失败: $e';
    }
  }

  // 清空所有历史记录
  Future<void> _clearAllHistories() async {
    try {
      await _historyService.clearAllHistories();
      _histories.value = [];
      _hasMoreData.value = false;
      _currentPage = 0;
    } catch (e) {
      _error.value = '清空历史记录失败: $e';
    }
  }

  // 显示删除确认对话框
  void _showDeleteConfirmDialog(History history) {
    showAdaptiveDialog(
      context: context,
      builder:
          (context) => FDialog(
            direction: Axis.vertical,
            title: const Text('删除历史记录'),
            body: Text('确定要删除 "${_extractFileName(history.url)}" 的观看历史吗？'),
            actions: [
              FButton(
                onPress: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              FButton(
                style: FButtonStyle.destructive(),
                onPress: () {
                  Navigator.pop(context);
                  _deleteHistory(history);
                },
                child: const Text('删除'),
              ),
            ],
          ),
    );
  }

  // 显示清空所有历史记录确认对话框
  void _showClearAllConfirmDialog() {
    showAdaptiveDialog(
      context: context,
      builder:
          (context) => FDialog(
            direction: Axis.vertical,
            title: const Text('清空所有历史记录'),
            body: const Text('确定要清空所有观看历史吗？此操作不可撤销。'),
            actions: [
              FButton(
                onPress: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              FButton(
                style: FButtonStyle.destructive(),
                onPress: () {
                  Navigator.pop(context);
                  _clearAllHistories();
                },
                child: const Text('清空'),
              ),
            ],
          ),
    );
  }

  // 从URL中提取文件名
  String _extractFileName(String? url) {
    if (url == null) return '未知文件';
    try {
      final fileName = path.basename(url);
      return fileName;
    } catch (e) {
      return url.split('/').last;
    }
  }

  // 播放视频
  Future<void> _playVideo(History history) async {
    try {
      final url = history.url;
      if (url == null) {
        _error.value = '视频URL为空';
        return;
      }
      switch (history.type) {
        case HistoriesType.mediaLibrary:
          _globalPlayerService.virtualVideoPath = url;
          if (!_globalPlayerService.setMediaLibraryIdFromPath()) {
            return;
          }
          final mediaLibrary = await _mediaLibraryService.getMediaLibrary(
            _globalPlayerService.mediaLibraryId!,
          );
          if (mediaLibrary == null) {
            _error.value = '找不到对应的媒体库';
            return;
          }
          // 设置FileExplorerService的provider和rootPath
          switch (mediaLibrary.mediaLibraryType) {
            case MediaLibraryType.webdav:
              final provider = WebDAVFileExplorerProvider(mediaLibrary);
              _fileExplorerService.setProvider(provider, mediaLibrary);
              break;
            case MediaLibraryType.local:
              final provider = LocalFileExplorerProvider();
              _fileExplorerService.setProvider(provider, mediaLibrary);
              break;
            default:
              _error.value = '不支持的媒体库类型';
              return;
          }
          if (!await _fileExplorerService.findVideo(url)) {
            _error.value = '找不到对应的文件';
            return;
          }
          _globalPlayerService.historiesType = HistoriesType.mediaLibrary;
          break;
        case HistoriesType.local:
          _globalPlayerService.virtualVideoPath = url;
          _globalPlayerService.currentVideoPath = url;
          _globalPlayerService.historiesType = HistoriesType.local;
          break;
        case HistoriesType.netWork:
          _globalPlayerService.virtualVideoPath = url;
          _globalPlayerService.currentVideoPath = url;
          _globalPlayerService.historiesType = HistoriesType.netWork;
          break;
      }
      // 导航到视频播放页面
      if (mounted) {
        final location = Uri(path: videoPlayerPath);
        context
            .push(location.toString())
            .then((context) => _refreshHistories());
      }
    } catch (e) {
      _error.value = '播放视频失败: $e';
    }
  }

  List<FItemMixin> _listBuilder(List<History> histories) {
    final widgetList = <FItemMixin>[];
    int itemCount = _histories.value.length + (_hasMoreData.value ? 1 : 0);
    for (var i = 0; i < itemCount; i++) {
      if (i == _histories.value.length) {
        widgetList.add(
          FItem(
            title: Center(
              child:
                  _isLoadingMore.value
                      ? Column(
                        children: [
                          const CircularProgressIndicator(),
                          const SizedBox(height: 8),
                          Text(
                            '正在加载更多历史记录...',
                            style: TextStyle(
                              color: context.theme.colors.mutedForeground,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      )
                      : _hasMoreData.value
                      ? ElevatedButton(
                        onPressed: _loadMoreHistories,
                        child: const Text('加载更多'),
                      )
                      : Text(
                        '已加载所有历史记录',
                        style: TextStyle(
                          color: context.theme.colors.mutedForeground,
                          fontSize: 12,
                        ),
                      ),
            ),
          ),
        );
        continue;
      }
      final history = _histories.value[i];
      final fileName = _extractFileName(history.url);
      widgetList.add(
        VideoItem(
          history: history,
          name: fileName,
          onPress: () => _playVideo(history),
          onLongPress: () => _showDeleteConfirmDialog(history),
        ),
      );
    }
    return widgetList;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        scrolledUnderElevation: 0,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '观看历史',
              style: context.theme.typography.xl.copyWith(height: 1.3),
            ),
            Watch((context) {
              if (_isLoading.value) {
                return Text(
                  '正在加载...',
                  style: context.theme.typography.xs.copyWith(
                    color: context.theme.colors.mutedForeground,
                  ),
                );
              } else if (_histories.value.isNotEmpty) {
                return Text(
                  '已加载 ${_histories.value.length} 条记录',
                  style: context.theme.typography.xs.copyWith(
                    color: context.theme.colors.mutedForeground,
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
        actions: [
          FButton.icon(
            style: FButtonStyle.ghost(),
            onPress: _refreshHistories,
            child: const Icon(Icons.refresh, size: 24),
          ),
          FButton.icon(
            style: FButtonStyle.ghost(),
            onPress: _showClearAllConfirmDialog,
            child: const Icon(Icons.clear_all, size: 24),
          ),
          const SizedBox(width: 6),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshHistories,
        child: Watch((context) {
          if (_isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }
          if (_error.value != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    _error.value!,
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _refreshHistories,
                    child: const Text('重试'),
                  ),
                ],
              ),
            );
          }
          if (_histories.value.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.history, size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  const Text(
                    '暂无观看历史',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '开始观看视频后，历史记录会显示在这里',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
            );
          }
          return CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            controller: _scrollController,
            slivers: [
              SliverToBoxAdapter(
                child: FItemGroup(
                  divider: FItemDivider.indented,
                  children: _listBuilder(_histories.value),
                ),
              ),
            ],
          );
        }),
      ),
    );
  }
}
