// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'storage.dart';

// ignore_for_file: type=lint
class $MediaLibrariesTable extends MediaLibraries
    with TableInfo<$MediaLibrariesTable, MediaLibrary> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $MediaLibrariesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
    'name',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _urlMeta = const VerificationMeta('url');
  @override
  late final GeneratedColumn<String> url = GeneratedColumn<String>(
    'url',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _portMeta = const VerificationMeta('port');
  @override
  late final GeneratedColumn<int> port = GeneratedColumn<int>(
    'port',
    aliasedName,
    true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _headersMeta = const VerificationMeta(
    'headers',
  );
  @override
  late final GeneratedColumn<String> headers = GeneratedColumn<String>(
    'headers',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  @override
  late final GeneratedColumnWithTypeConverter<MediaLibraryType, int>
  mediaLibraryType = GeneratedColumn<int>(
    'media_library_type',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  ).withConverter<MediaLibraryType>(
    $MediaLibrariesTable.$convertermediaLibraryType,
  );
  static const VerificationMeta _accountMeta = const VerificationMeta(
    'account',
  );
  @override
  late final GeneratedColumn<String> account = GeneratedColumn<String>(
    'account',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _passwordMeta = const VerificationMeta(
    'password',
  );
  @override
  late final GeneratedColumn<String> password = GeneratedColumn<String>(
    'password',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _isAnonymousMeta = const VerificationMeta(
    'isAnonymous',
  );
  @override
  late final GeneratedColumn<bool> isAnonymous = GeneratedColumn<bool>(
    'is_anonymous',
    aliasedName,
    false,
    type: DriftSqlType.bool,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'CHECK ("is_anonymous" IN (0, 1))',
    ),
    defaultValue: const Constant(false),
  );
  @override
  List<GeneratedColumn> get $columns => [
    id,
    name,
    url,
    port,
    headers,
    mediaLibraryType,
    account,
    password,
    isAnonymous,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'media_libraries';
  @override
  VerificationContext validateIntegrity(
    Insertable<MediaLibrary> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('name')) {
      context.handle(
        _nameMeta,
        name.isAcceptableOrUnknown(data['name']!, _nameMeta),
      );
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('url')) {
      context.handle(
        _urlMeta,
        url.isAcceptableOrUnknown(data['url']!, _urlMeta),
      );
    } else if (isInserting) {
      context.missing(_urlMeta);
    }
    if (data.containsKey('port')) {
      context.handle(
        _portMeta,
        port.isAcceptableOrUnknown(data['port']!, _portMeta),
      );
    }
    if (data.containsKey('headers')) {
      context.handle(
        _headersMeta,
        headers.isAcceptableOrUnknown(data['headers']!, _headersMeta),
      );
    } else if (isInserting) {
      context.missing(_headersMeta);
    }
    if (data.containsKey('account')) {
      context.handle(
        _accountMeta,
        account.isAcceptableOrUnknown(data['account']!, _accountMeta),
      );
    }
    if (data.containsKey('password')) {
      context.handle(
        _passwordMeta,
        password.isAcceptableOrUnknown(data['password']!, _passwordMeta),
      );
    }
    if (data.containsKey('is_anonymous')) {
      context.handle(
        _isAnonymousMeta,
        isAnonymous.isAcceptableOrUnknown(
          data['is_anonymous']!,
          _isAnonymousMeta,
        ),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  MediaLibrary map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return MediaLibrary(
      id:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}id'],
          )!,
      name:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}name'],
          )!,
      url:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}url'],
          )!,
      port: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}port'],
      ),
      headers:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}headers'],
          )!,
      mediaLibraryType: $MediaLibrariesTable.$convertermediaLibraryType.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.int,
          data['${effectivePrefix}media_library_type'],
        )!,
      ),
      account: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}account'],
      ),
      password: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}password'],
      ),
      isAnonymous:
          attachedDatabase.typeMapping.read(
            DriftSqlType.bool,
            data['${effectivePrefix}is_anonymous'],
          )!,
    );
  }

  @override
  $MediaLibrariesTable createAlias(String alias) {
    return $MediaLibrariesTable(attachedDatabase, alias);
  }

  static JsonTypeConverter2<MediaLibraryType, int, int>
  $convertermediaLibraryType = const EnumIndexConverter<MediaLibraryType>(
    MediaLibraryType.values,
  );
}

class MediaLibrary extends DataClass implements Insertable<MediaLibrary> {
  final int id;
  final String name;
  final String url;
  final int? port;
  final String headers;
  final MediaLibraryType mediaLibraryType;
  final String? account;
  final String? password;
  final bool isAnonymous;
  const MediaLibrary({
    required this.id,
    required this.name,
    required this.url,
    this.port,
    required this.headers,
    required this.mediaLibraryType,
    this.account,
    this.password,
    required this.isAnonymous,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['name'] = Variable<String>(name);
    map['url'] = Variable<String>(url);
    if (!nullToAbsent || port != null) {
      map['port'] = Variable<int>(port);
    }
    map['headers'] = Variable<String>(headers);
    {
      map['media_library_type'] = Variable<int>(
        $MediaLibrariesTable.$convertermediaLibraryType.toSql(mediaLibraryType),
      );
    }
    if (!nullToAbsent || account != null) {
      map['account'] = Variable<String>(account);
    }
    if (!nullToAbsent || password != null) {
      map['password'] = Variable<String>(password);
    }
    map['is_anonymous'] = Variable<bool>(isAnonymous);
    return map;
  }

  MediaLibrariesCompanion toCompanion(bool nullToAbsent) {
    return MediaLibrariesCompanion(
      id: Value(id),
      name: Value(name),
      url: Value(url),
      port: port == null && nullToAbsent ? const Value.absent() : Value(port),
      headers: Value(headers),
      mediaLibraryType: Value(mediaLibraryType),
      account:
          account == null && nullToAbsent
              ? const Value.absent()
              : Value(account),
      password:
          password == null && nullToAbsent
              ? const Value.absent()
              : Value(password),
      isAnonymous: Value(isAnonymous),
    );
  }

  factory MediaLibrary.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return MediaLibrary(
      id: serializer.fromJson<int>(json['id']),
      name: serializer.fromJson<String>(json['name']),
      url: serializer.fromJson<String>(json['url']),
      port: serializer.fromJson<int?>(json['port']),
      headers: serializer.fromJson<String>(json['headers']),
      mediaLibraryType: $MediaLibrariesTable.$convertermediaLibraryType
          .fromJson(serializer.fromJson<int>(json['mediaLibraryType'])),
      account: serializer.fromJson<String?>(json['account']),
      password: serializer.fromJson<String?>(json['password']),
      isAnonymous: serializer.fromJson<bool>(json['isAnonymous']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'name': serializer.toJson<String>(name),
      'url': serializer.toJson<String>(url),
      'port': serializer.toJson<int?>(port),
      'headers': serializer.toJson<String>(headers),
      'mediaLibraryType': serializer.toJson<int>(
        $MediaLibrariesTable.$convertermediaLibraryType.toJson(
          mediaLibraryType,
        ),
      ),
      'account': serializer.toJson<String?>(account),
      'password': serializer.toJson<String?>(password),
      'isAnonymous': serializer.toJson<bool>(isAnonymous),
    };
  }

  MediaLibrary copyWith({
    int? id,
    String? name,
    String? url,
    Value<int?> port = const Value.absent(),
    String? headers,
    MediaLibraryType? mediaLibraryType,
    Value<String?> account = const Value.absent(),
    Value<String?> password = const Value.absent(),
    bool? isAnonymous,
  }) => MediaLibrary(
    id: id ?? this.id,
    name: name ?? this.name,
    url: url ?? this.url,
    port: port.present ? port.value : this.port,
    headers: headers ?? this.headers,
    mediaLibraryType: mediaLibraryType ?? this.mediaLibraryType,
    account: account.present ? account.value : this.account,
    password: password.present ? password.value : this.password,
    isAnonymous: isAnonymous ?? this.isAnonymous,
  );
  MediaLibrary copyWithCompanion(MediaLibrariesCompanion data) {
    return MediaLibrary(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
      url: data.url.present ? data.url.value : this.url,
      port: data.port.present ? data.port.value : this.port,
      headers: data.headers.present ? data.headers.value : this.headers,
      mediaLibraryType:
          data.mediaLibraryType.present
              ? data.mediaLibraryType.value
              : this.mediaLibraryType,
      account: data.account.present ? data.account.value : this.account,
      password: data.password.present ? data.password.value : this.password,
      isAnonymous:
          data.isAnonymous.present ? data.isAnonymous.value : this.isAnonymous,
    );
  }

  @override
  String toString() {
    return (StringBuffer('MediaLibrary(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('url: $url, ')
          ..write('port: $port, ')
          ..write('headers: $headers, ')
          ..write('mediaLibraryType: $mediaLibraryType, ')
          ..write('account: $account, ')
          ..write('password: $password, ')
          ..write('isAnonymous: $isAnonymous')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    id,
    name,
    url,
    port,
    headers,
    mediaLibraryType,
    account,
    password,
    isAnonymous,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is MediaLibrary &&
          other.id == this.id &&
          other.name == this.name &&
          other.url == this.url &&
          other.port == this.port &&
          other.headers == this.headers &&
          other.mediaLibraryType == this.mediaLibraryType &&
          other.account == this.account &&
          other.password == this.password &&
          other.isAnonymous == this.isAnonymous);
}

class MediaLibrariesCompanion extends UpdateCompanion<MediaLibrary> {
  final Value<int> id;
  final Value<String> name;
  final Value<String> url;
  final Value<int?> port;
  final Value<String> headers;
  final Value<MediaLibraryType> mediaLibraryType;
  final Value<String?> account;
  final Value<String?> password;
  final Value<bool> isAnonymous;
  const MediaLibrariesCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
    this.url = const Value.absent(),
    this.port = const Value.absent(),
    this.headers = const Value.absent(),
    this.mediaLibraryType = const Value.absent(),
    this.account = const Value.absent(),
    this.password = const Value.absent(),
    this.isAnonymous = const Value.absent(),
  });
  MediaLibrariesCompanion.insert({
    this.id = const Value.absent(),
    required String name,
    required String url,
    this.port = const Value.absent(),
    required String headers,
    required MediaLibraryType mediaLibraryType,
    this.account = const Value.absent(),
    this.password = const Value.absent(),
    this.isAnonymous = const Value.absent(),
  }) : name = Value(name),
       url = Value(url),
       headers = Value(headers),
       mediaLibraryType = Value(mediaLibraryType);
  static Insertable<MediaLibrary> custom({
    Expression<int>? id,
    Expression<String>? name,
    Expression<String>? url,
    Expression<int>? port,
    Expression<String>? headers,
    Expression<int>? mediaLibraryType,
    Expression<String>? account,
    Expression<String>? password,
    Expression<bool>? isAnonymous,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (url != null) 'url': url,
      if (port != null) 'port': port,
      if (headers != null) 'headers': headers,
      if (mediaLibraryType != null) 'media_library_type': mediaLibraryType,
      if (account != null) 'account': account,
      if (password != null) 'password': password,
      if (isAnonymous != null) 'is_anonymous': isAnonymous,
    });
  }

  MediaLibrariesCompanion copyWith({
    Value<int>? id,
    Value<String>? name,
    Value<String>? url,
    Value<int?>? port,
    Value<String>? headers,
    Value<MediaLibraryType>? mediaLibraryType,
    Value<String?>? account,
    Value<String?>? password,
    Value<bool>? isAnonymous,
  }) {
    return MediaLibrariesCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
      url: url ?? this.url,
      port: port ?? this.port,
      headers: headers ?? this.headers,
      mediaLibraryType: mediaLibraryType ?? this.mediaLibraryType,
      account: account ?? this.account,
      password: password ?? this.password,
      isAnonymous: isAnonymous ?? this.isAnonymous,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (url.present) {
      map['url'] = Variable<String>(url.value);
    }
    if (port.present) {
      map['port'] = Variable<int>(port.value);
    }
    if (headers.present) {
      map['headers'] = Variable<String>(headers.value);
    }
    if (mediaLibraryType.present) {
      map['media_library_type'] = Variable<int>(
        $MediaLibrariesTable.$convertermediaLibraryType.toSql(
          mediaLibraryType.value,
        ),
      );
    }
    if (account.present) {
      map['account'] = Variable<String>(account.value);
    }
    if (password.present) {
      map['password'] = Variable<String>(password.value);
    }
    if (isAnonymous.present) {
      map['is_anonymous'] = Variable<bool>(isAnonymous.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('MediaLibrariesCompanion(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('url: $url, ')
          ..write('port: $port, ')
          ..write('headers: $headers, ')
          ..write('mediaLibraryType: $mediaLibraryType, ')
          ..write('account: $account, ')
          ..write('password: $password, ')
          ..write('isAnonymous: $isAnonymous')
          ..write(')'))
        .toString();
  }
}

class $HistoriesTable extends Histories
    with TableInfo<$HistoriesTable, History> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $HistoriesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _uniqueKeyMeta = const VerificationMeta(
    'uniqueKey',
  );
  @override
  late final GeneratedColumn<String> uniqueKey = GeneratedColumn<String>(
    'unique_key',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
    defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'),
  );
  static const VerificationMeta _durationMeta = const VerificationMeta(
    'duration',
  );
  @override
  late final GeneratedColumn<int> duration = GeneratedColumn<int>(
    'duration',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _positionMeta = const VerificationMeta(
    'position',
  );
  @override
  late final GeneratedColumn<int> position = GeneratedColumn<int>(
    'position',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _urlMeta = const VerificationMeta('url');
  @override
  late final GeneratedColumn<String> url = GeneratedColumn<String>(
    'url',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _headersMeta = const VerificationMeta(
    'headers',
  );
  @override
  late final GeneratedColumn<String> headers = GeneratedColumn<String>(
    'headers',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  @override
  late final GeneratedColumnWithTypeConverter<HistoriesType, int> type =
      GeneratedColumn<int>(
        'type',
        aliasedName,
        false,
        type: DriftSqlType.int,
        requiredDuringInsert: true,
      ).withConverter<HistoriesType>($HistoriesTable.$convertertype);
  static const VerificationMeta _mediaLibraryIdMeta = const VerificationMeta(
    'mediaLibraryId',
  );
  @override
  late final GeneratedColumn<int> mediaLibraryId = GeneratedColumn<int>(
    'media_library_id',
    aliasedName,
    true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _updateTimeMeta = const VerificationMeta(
    'updateTime',
  );
  @override
  late final GeneratedColumn<int> updateTime = GeneratedColumn<int>(
    'update_time',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  @override
  List<GeneratedColumn> get $columns => [
    id,
    uniqueKey,
    duration,
    position,
    url,
    headers,
    type,
    mediaLibraryId,
    updateTime,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'histories';
  @override
  VerificationContext validateIntegrity(
    Insertable<History> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('unique_key')) {
      context.handle(
        _uniqueKeyMeta,
        uniqueKey.isAcceptableOrUnknown(data['unique_key']!, _uniqueKeyMeta),
      );
    } else if (isInserting) {
      context.missing(_uniqueKeyMeta);
    }
    if (data.containsKey('duration')) {
      context.handle(
        _durationMeta,
        duration.isAcceptableOrUnknown(data['duration']!, _durationMeta),
      );
    } else if (isInserting) {
      context.missing(_durationMeta);
    }
    if (data.containsKey('position')) {
      context.handle(
        _positionMeta,
        position.isAcceptableOrUnknown(data['position']!, _positionMeta),
      );
    } else if (isInserting) {
      context.missing(_positionMeta);
    }
    if (data.containsKey('url')) {
      context.handle(
        _urlMeta,
        url.isAcceptableOrUnknown(data['url']!, _urlMeta),
      );
    }
    if (data.containsKey('headers')) {
      context.handle(
        _headersMeta,
        headers.isAcceptableOrUnknown(data['headers']!, _headersMeta),
      );
    }
    if (data.containsKey('media_library_id')) {
      context.handle(
        _mediaLibraryIdMeta,
        mediaLibraryId.isAcceptableOrUnknown(
          data['media_library_id']!,
          _mediaLibraryIdMeta,
        ),
      );
    }
    if (data.containsKey('update_time')) {
      context.handle(
        _updateTimeMeta,
        updateTime.isAcceptableOrUnknown(data['update_time']!, _updateTimeMeta),
      );
    } else if (isInserting) {
      context.missing(_updateTimeMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  History map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return History(
      id:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}id'],
          )!,
      uniqueKey:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}unique_key'],
          )!,
      duration:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}duration'],
          )!,
      position:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}position'],
          )!,
      url: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}url'],
      ),
      headers: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}headers'],
      ),
      type: $HistoriesTable.$convertertype.fromSql(
        attachedDatabase.typeMapping.read(
          DriftSqlType.int,
          data['${effectivePrefix}type'],
        )!,
      ),
      mediaLibraryId: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}media_library_id'],
      ),
      updateTime:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}update_time'],
          )!,
    );
  }

  @override
  $HistoriesTable createAlias(String alias) {
    return $HistoriesTable(attachedDatabase, alias);
  }

  static JsonTypeConverter2<HistoriesType, int, int> $convertertype =
      const EnumIndexConverter<HistoriesType>(HistoriesType.values);
}

class History extends DataClass implements Insertable<History> {
  final int id;
  final String uniqueKey;
  final int duration;
  final int position;
  final String? url;
  final String? headers;
  final HistoriesType type;
  final int? mediaLibraryId;
  final int updateTime;
  const History({
    required this.id,
    required this.uniqueKey,
    required this.duration,
    required this.position,
    this.url,
    this.headers,
    required this.type,
    this.mediaLibraryId,
    required this.updateTime,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['unique_key'] = Variable<String>(uniqueKey);
    map['duration'] = Variable<int>(duration);
    map['position'] = Variable<int>(position);
    if (!nullToAbsent || url != null) {
      map['url'] = Variable<String>(url);
    }
    if (!nullToAbsent || headers != null) {
      map['headers'] = Variable<String>(headers);
    }
    {
      map['type'] = Variable<int>($HistoriesTable.$convertertype.toSql(type));
    }
    if (!nullToAbsent || mediaLibraryId != null) {
      map['media_library_id'] = Variable<int>(mediaLibraryId);
    }
    map['update_time'] = Variable<int>(updateTime);
    return map;
  }

  HistoriesCompanion toCompanion(bool nullToAbsent) {
    return HistoriesCompanion(
      id: Value(id),
      uniqueKey: Value(uniqueKey),
      duration: Value(duration),
      position: Value(position),
      url: url == null && nullToAbsent ? const Value.absent() : Value(url),
      headers:
          headers == null && nullToAbsent
              ? const Value.absent()
              : Value(headers),
      type: Value(type),
      mediaLibraryId:
          mediaLibraryId == null && nullToAbsent
              ? const Value.absent()
              : Value(mediaLibraryId),
      updateTime: Value(updateTime),
    );
  }

  factory History.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return History(
      id: serializer.fromJson<int>(json['id']),
      uniqueKey: serializer.fromJson<String>(json['uniqueKey']),
      duration: serializer.fromJson<int>(json['duration']),
      position: serializer.fromJson<int>(json['position']),
      url: serializer.fromJson<String?>(json['url']),
      headers: serializer.fromJson<String?>(json['headers']),
      type: $HistoriesTable.$convertertype.fromJson(
        serializer.fromJson<int>(json['type']),
      ),
      mediaLibraryId: serializer.fromJson<int?>(json['mediaLibraryId']),
      updateTime: serializer.fromJson<int>(json['updateTime']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'uniqueKey': serializer.toJson<String>(uniqueKey),
      'duration': serializer.toJson<int>(duration),
      'position': serializer.toJson<int>(position),
      'url': serializer.toJson<String?>(url),
      'headers': serializer.toJson<String?>(headers),
      'type': serializer.toJson<int>(
        $HistoriesTable.$convertertype.toJson(type),
      ),
      'mediaLibraryId': serializer.toJson<int?>(mediaLibraryId),
      'updateTime': serializer.toJson<int>(updateTime),
    };
  }

  History copyWith({
    int? id,
    String? uniqueKey,
    int? duration,
    int? position,
    Value<String?> url = const Value.absent(),
    Value<String?> headers = const Value.absent(),
    HistoriesType? type,
    Value<int?> mediaLibraryId = const Value.absent(),
    int? updateTime,
  }) => History(
    id: id ?? this.id,
    uniqueKey: uniqueKey ?? this.uniqueKey,
    duration: duration ?? this.duration,
    position: position ?? this.position,
    url: url.present ? url.value : this.url,
    headers: headers.present ? headers.value : this.headers,
    type: type ?? this.type,
    mediaLibraryId:
        mediaLibraryId.present ? mediaLibraryId.value : this.mediaLibraryId,
    updateTime: updateTime ?? this.updateTime,
  );
  History copyWithCompanion(HistoriesCompanion data) {
    return History(
      id: data.id.present ? data.id.value : this.id,
      uniqueKey: data.uniqueKey.present ? data.uniqueKey.value : this.uniqueKey,
      duration: data.duration.present ? data.duration.value : this.duration,
      position: data.position.present ? data.position.value : this.position,
      url: data.url.present ? data.url.value : this.url,
      headers: data.headers.present ? data.headers.value : this.headers,
      type: data.type.present ? data.type.value : this.type,
      mediaLibraryId:
          data.mediaLibraryId.present
              ? data.mediaLibraryId.value
              : this.mediaLibraryId,
      updateTime:
          data.updateTime.present ? data.updateTime.value : this.updateTime,
    );
  }

  @override
  String toString() {
    return (StringBuffer('History(')
          ..write('id: $id, ')
          ..write('uniqueKey: $uniqueKey, ')
          ..write('duration: $duration, ')
          ..write('position: $position, ')
          ..write('url: $url, ')
          ..write('headers: $headers, ')
          ..write('type: $type, ')
          ..write('mediaLibraryId: $mediaLibraryId, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    id,
    uniqueKey,
    duration,
    position,
    url,
    headers,
    type,
    mediaLibraryId,
    updateTime,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is History &&
          other.id == this.id &&
          other.uniqueKey == this.uniqueKey &&
          other.duration == this.duration &&
          other.position == this.position &&
          other.url == this.url &&
          other.headers == this.headers &&
          other.type == this.type &&
          other.mediaLibraryId == this.mediaLibraryId &&
          other.updateTime == this.updateTime);
}

class HistoriesCompanion extends UpdateCompanion<History> {
  final Value<int> id;
  final Value<String> uniqueKey;
  final Value<int> duration;
  final Value<int> position;
  final Value<String?> url;
  final Value<String?> headers;
  final Value<HistoriesType> type;
  final Value<int?> mediaLibraryId;
  final Value<int> updateTime;
  const HistoriesCompanion({
    this.id = const Value.absent(),
    this.uniqueKey = const Value.absent(),
    this.duration = const Value.absent(),
    this.position = const Value.absent(),
    this.url = const Value.absent(),
    this.headers = const Value.absent(),
    this.type = const Value.absent(),
    this.mediaLibraryId = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  HistoriesCompanion.insert({
    this.id = const Value.absent(),
    required String uniqueKey,
    required int duration,
    required int position,
    this.url = const Value.absent(),
    this.headers = const Value.absent(),
    required HistoriesType type,
    this.mediaLibraryId = const Value.absent(),
    required int updateTime,
  }) : uniqueKey = Value(uniqueKey),
       duration = Value(duration),
       position = Value(position),
       type = Value(type),
       updateTime = Value(updateTime);
  static Insertable<History> custom({
    Expression<int>? id,
    Expression<String>? uniqueKey,
    Expression<int>? duration,
    Expression<int>? position,
    Expression<String>? url,
    Expression<String>? headers,
    Expression<int>? type,
    Expression<int>? mediaLibraryId,
    Expression<int>? updateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (uniqueKey != null) 'unique_key': uniqueKey,
      if (duration != null) 'duration': duration,
      if (position != null) 'position': position,
      if (url != null) 'url': url,
      if (headers != null) 'headers': headers,
      if (type != null) 'type': type,
      if (mediaLibraryId != null) 'media_library_id': mediaLibraryId,
      if (updateTime != null) 'update_time': updateTime,
    });
  }

  HistoriesCompanion copyWith({
    Value<int>? id,
    Value<String>? uniqueKey,
    Value<int>? duration,
    Value<int>? position,
    Value<String?>? url,
    Value<String?>? headers,
    Value<HistoriesType>? type,
    Value<int?>? mediaLibraryId,
    Value<int>? updateTime,
  }) {
    return HistoriesCompanion(
      id: id ?? this.id,
      uniqueKey: uniqueKey ?? this.uniqueKey,
      duration: duration ?? this.duration,
      position: position ?? this.position,
      url: url ?? this.url,
      headers: headers ?? this.headers,
      type: type ?? this.type,
      mediaLibraryId: mediaLibraryId ?? this.mediaLibraryId,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (uniqueKey.present) {
      map['unique_key'] = Variable<String>(uniqueKey.value);
    }
    if (duration.present) {
      map['duration'] = Variable<int>(duration.value);
    }
    if (position.present) {
      map['position'] = Variable<int>(position.value);
    }
    if (url.present) {
      map['url'] = Variable<String>(url.value);
    }
    if (headers.present) {
      map['headers'] = Variable<String>(headers.value);
    }
    if (type.present) {
      map['type'] = Variable<int>(
        $HistoriesTable.$convertertype.toSql(type.value),
      );
    }
    if (mediaLibraryId.present) {
      map['media_library_id'] = Variable<int>(mediaLibraryId.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<int>(updateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('HistoriesCompanion(')
          ..write('id: $id, ')
          ..write('uniqueKey: $uniqueKey, ')
          ..write('duration: $duration, ')
          ..write('position: $position, ')
          ..write('url: $url, ')
          ..write('headers: $headers, ')
          ..write('type: $type, ')
          ..write('mediaLibraryId: $mediaLibraryId, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }
}

abstract class _$StorageService extends GeneratedDatabase {
  _$StorageService(QueryExecutor e) : super(e);
  $StorageServiceManager get managers => $StorageServiceManager(this);
  late final $MediaLibrariesTable mediaLibraries = $MediaLibrariesTable(this);
  late final $HistoriesTable histories = $HistoriesTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
    mediaLibraries,
    histories,
  ];
}

typedef $$MediaLibrariesTableCreateCompanionBuilder =
    MediaLibrariesCompanion Function({
      Value<int> id,
      required String name,
      required String url,
      Value<int?> port,
      required String headers,
      required MediaLibraryType mediaLibraryType,
      Value<String?> account,
      Value<String?> password,
      Value<bool> isAnonymous,
    });
typedef $$MediaLibrariesTableUpdateCompanionBuilder =
    MediaLibrariesCompanion Function({
      Value<int> id,
      Value<String> name,
      Value<String> url,
      Value<int?> port,
      Value<String> headers,
      Value<MediaLibraryType> mediaLibraryType,
      Value<String?> account,
      Value<String?> password,
      Value<bool> isAnonymous,
    });

class $$MediaLibrariesTableFilterComposer
    extends Composer<_$StorageService, $MediaLibrariesTable> {
  $$MediaLibrariesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get name => $composableBuilder(
    column: $table.name,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get url => $composableBuilder(
    column: $table.url,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get port => $composableBuilder(
    column: $table.port,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get headers => $composableBuilder(
    column: $table.headers,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<MediaLibraryType, MediaLibraryType, int>
  get mediaLibraryType => $composableBuilder(
    column: $table.mediaLibraryType,
    builder: (column) => ColumnWithTypeConverterFilters(column),
  );

  ColumnFilters<String> get account => $composableBuilder(
    column: $table.account,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get password => $composableBuilder(
    column: $table.password,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<bool> get isAnonymous => $composableBuilder(
    column: $table.isAnonymous,
    builder: (column) => ColumnFilters(column),
  );
}

class $$MediaLibrariesTableOrderingComposer
    extends Composer<_$StorageService, $MediaLibrariesTable> {
  $$MediaLibrariesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get name => $composableBuilder(
    column: $table.name,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get url => $composableBuilder(
    column: $table.url,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get port => $composableBuilder(
    column: $table.port,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get headers => $composableBuilder(
    column: $table.headers,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get mediaLibraryType => $composableBuilder(
    column: $table.mediaLibraryType,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get account => $composableBuilder(
    column: $table.account,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get password => $composableBuilder(
    column: $table.password,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<bool> get isAnonymous => $composableBuilder(
    column: $table.isAnonymous,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$MediaLibrariesTableAnnotationComposer
    extends Composer<_$StorageService, $MediaLibrariesTable> {
  $$MediaLibrariesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get url =>
      $composableBuilder(column: $table.url, builder: (column) => column);

  GeneratedColumn<int> get port =>
      $composableBuilder(column: $table.port, builder: (column) => column);

  GeneratedColumn<String> get headers =>
      $composableBuilder(column: $table.headers, builder: (column) => column);

  GeneratedColumnWithTypeConverter<MediaLibraryType, int>
  get mediaLibraryType => $composableBuilder(
    column: $table.mediaLibraryType,
    builder: (column) => column,
  );

  GeneratedColumn<String> get account =>
      $composableBuilder(column: $table.account, builder: (column) => column);

  GeneratedColumn<String> get password =>
      $composableBuilder(column: $table.password, builder: (column) => column);

  GeneratedColumn<bool> get isAnonymous => $composableBuilder(
    column: $table.isAnonymous,
    builder: (column) => column,
  );
}

class $$MediaLibrariesTableTableManager
    extends
        RootTableManager<
          _$StorageService,
          $MediaLibrariesTable,
          MediaLibrary,
          $$MediaLibrariesTableFilterComposer,
          $$MediaLibrariesTableOrderingComposer,
          $$MediaLibrariesTableAnnotationComposer,
          $$MediaLibrariesTableCreateCompanionBuilder,
          $$MediaLibrariesTableUpdateCompanionBuilder,
          (
            MediaLibrary,
            BaseReferences<
              _$StorageService,
              $MediaLibrariesTable,
              MediaLibrary
            >,
          ),
          MediaLibrary,
          PrefetchHooks Function()
        > {
  $$MediaLibrariesTableTableManager(
    _$StorageService db,
    $MediaLibrariesTable table,
  ) : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$MediaLibrariesTableFilterComposer($db: db, $table: table),
          createOrderingComposer:
              () =>
                  $$MediaLibrariesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer:
              () => $$MediaLibrariesTableAnnotationComposer(
                $db: db,
                $table: table,
              ),
          updateCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<String> name = const Value.absent(),
                Value<String> url = const Value.absent(),
                Value<int?> port = const Value.absent(),
                Value<String> headers = const Value.absent(),
                Value<MediaLibraryType> mediaLibraryType = const Value.absent(),
                Value<String?> account = const Value.absent(),
                Value<String?> password = const Value.absent(),
                Value<bool> isAnonymous = const Value.absent(),
              }) => MediaLibrariesCompanion(
                id: id,
                name: name,
                url: url,
                port: port,
                headers: headers,
                mediaLibraryType: mediaLibraryType,
                account: account,
                password: password,
                isAnonymous: isAnonymous,
              ),
          createCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                required String name,
                required String url,
                Value<int?> port = const Value.absent(),
                required String headers,
                required MediaLibraryType mediaLibraryType,
                Value<String?> account = const Value.absent(),
                Value<String?> password = const Value.absent(),
                Value<bool> isAnonymous = const Value.absent(),
              }) => MediaLibrariesCompanion.insert(
                id: id,
                name: name,
                url: url,
                port: port,
                headers: headers,
                mediaLibraryType: mediaLibraryType,
                account: account,
                password: password,
                isAnonymous: isAnonymous,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          BaseReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$MediaLibrariesTableProcessedTableManager =
    ProcessedTableManager<
      _$StorageService,
      $MediaLibrariesTable,
      MediaLibrary,
      $$MediaLibrariesTableFilterComposer,
      $$MediaLibrariesTableOrderingComposer,
      $$MediaLibrariesTableAnnotationComposer,
      $$MediaLibrariesTableCreateCompanionBuilder,
      $$MediaLibrariesTableUpdateCompanionBuilder,
      (
        MediaLibrary,
        BaseReferences<_$StorageService, $MediaLibrariesTable, MediaLibrary>,
      ),
      MediaLibrary,
      PrefetchHooks Function()
    >;
typedef $$HistoriesTableCreateCompanionBuilder =
    HistoriesCompanion Function({
      Value<int> id,
      required String uniqueKey,
      required int duration,
      required int position,
      Value<String?> url,
      Value<String?> headers,
      required HistoriesType type,
      Value<int?> mediaLibraryId,
      required int updateTime,
    });
typedef $$HistoriesTableUpdateCompanionBuilder =
    HistoriesCompanion Function({
      Value<int> id,
      Value<String> uniqueKey,
      Value<int> duration,
      Value<int> position,
      Value<String?> url,
      Value<String?> headers,
      Value<HistoriesType> type,
      Value<int?> mediaLibraryId,
      Value<int> updateTime,
    });

class $$HistoriesTableFilterComposer
    extends Composer<_$StorageService, $HistoriesTable> {
  $$HistoriesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get uniqueKey => $composableBuilder(
    column: $table.uniqueKey,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get duration => $composableBuilder(
    column: $table.duration,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get position => $composableBuilder(
    column: $table.position,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get url => $composableBuilder(
    column: $table.url,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get headers => $composableBuilder(
    column: $table.headers,
    builder: (column) => ColumnFilters(column),
  );

  ColumnWithTypeConverterFilters<HistoriesType, HistoriesType, int> get type =>
      $composableBuilder(
        column: $table.type,
        builder: (column) => ColumnWithTypeConverterFilters(column),
      );

  ColumnFilters<int> get mediaLibraryId => $composableBuilder(
    column: $table.mediaLibraryId,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get updateTime => $composableBuilder(
    column: $table.updateTime,
    builder: (column) => ColumnFilters(column),
  );
}

class $$HistoriesTableOrderingComposer
    extends Composer<_$StorageService, $HistoriesTable> {
  $$HistoriesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get uniqueKey => $composableBuilder(
    column: $table.uniqueKey,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get duration => $composableBuilder(
    column: $table.duration,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get position => $composableBuilder(
    column: $table.position,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get url => $composableBuilder(
    column: $table.url,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get headers => $composableBuilder(
    column: $table.headers,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get type => $composableBuilder(
    column: $table.type,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get mediaLibraryId => $composableBuilder(
    column: $table.mediaLibraryId,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get updateTime => $composableBuilder(
    column: $table.updateTime,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$HistoriesTableAnnotationComposer
    extends Composer<_$StorageService, $HistoriesTable> {
  $$HistoriesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get uniqueKey =>
      $composableBuilder(column: $table.uniqueKey, builder: (column) => column);

  GeneratedColumn<int> get duration =>
      $composableBuilder(column: $table.duration, builder: (column) => column);

  GeneratedColumn<int> get position =>
      $composableBuilder(column: $table.position, builder: (column) => column);

  GeneratedColumn<String> get url =>
      $composableBuilder(column: $table.url, builder: (column) => column);

  GeneratedColumn<String> get headers =>
      $composableBuilder(column: $table.headers, builder: (column) => column);

  GeneratedColumnWithTypeConverter<HistoriesType, int> get type =>
      $composableBuilder(column: $table.type, builder: (column) => column);

  GeneratedColumn<int> get mediaLibraryId => $composableBuilder(
    column: $table.mediaLibraryId,
    builder: (column) => column,
  );

  GeneratedColumn<int> get updateTime => $composableBuilder(
    column: $table.updateTime,
    builder: (column) => column,
  );
}

class $$HistoriesTableTableManager
    extends
        RootTableManager<
          _$StorageService,
          $HistoriesTable,
          History,
          $$HistoriesTableFilterComposer,
          $$HistoriesTableOrderingComposer,
          $$HistoriesTableAnnotationComposer,
          $$HistoriesTableCreateCompanionBuilder,
          $$HistoriesTableUpdateCompanionBuilder,
          (History, BaseReferences<_$StorageService, $HistoriesTable, History>),
          History,
          PrefetchHooks Function()
        > {
  $$HistoriesTableTableManager(_$StorageService db, $HistoriesTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$HistoriesTableFilterComposer($db: db, $table: table),
          createOrderingComposer:
              () => $$HistoriesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer:
              () => $$HistoriesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<String> uniqueKey = const Value.absent(),
                Value<int> duration = const Value.absent(),
                Value<int> position = const Value.absent(),
                Value<String?> url = const Value.absent(),
                Value<String?> headers = const Value.absent(),
                Value<HistoriesType> type = const Value.absent(),
                Value<int?> mediaLibraryId = const Value.absent(),
                Value<int> updateTime = const Value.absent(),
              }) => HistoriesCompanion(
                id: id,
                uniqueKey: uniqueKey,
                duration: duration,
                position: position,
                url: url,
                headers: headers,
                type: type,
                mediaLibraryId: mediaLibraryId,
                updateTime: updateTime,
              ),
          createCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                required String uniqueKey,
                required int duration,
                required int position,
                Value<String?> url = const Value.absent(),
                Value<String?> headers = const Value.absent(),
                required HistoriesType type,
                Value<int?> mediaLibraryId = const Value.absent(),
                required int updateTime,
              }) => HistoriesCompanion.insert(
                id: id,
                uniqueKey: uniqueKey,
                duration: duration,
                position: position,
                url: url,
                headers: headers,
                type: type,
                mediaLibraryId: mediaLibraryId,
                updateTime: updateTime,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          BaseReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$HistoriesTableProcessedTableManager =
    ProcessedTableManager<
      _$StorageService,
      $HistoriesTable,
      History,
      $$HistoriesTableFilterComposer,
      $$HistoriesTableOrderingComposer,
      $$HistoriesTableAnnotationComposer,
      $$HistoriesTableCreateCompanionBuilder,
      $$HistoriesTableUpdateCompanionBuilder,
      (History, BaseReferences<_$StorageService, $HistoriesTable, History>),
      History,
      PrefetchHooks Function()
    >;

class $StorageServiceManager {
  final _$StorageService _db;
  $StorageServiceManager(this._db);
  $$MediaLibrariesTableTableManager get mediaLibraries =>
      $$MediaLibrariesTableTableManager(_db, _db.mediaLibraries);
  $$HistoriesTableTableManager get histories =>
      $$HistoriesTableTableManager(_db, _db.histories);
}
