import 'dart:async';

import 'package:dandanplay_flutter/service/storage.dart';
import 'package:get_it/get_it.dart';
import 'package:drift/drift.dart';

import '../utils/crypto_utils.dart';

class HistoryService {
  final StorageService storage;

  HistoryService({required this.storage});

  static Future<HistoryService> register(StorageService ss) async {
    final service = HistoryService(storage: ss);
    GetIt.I.registerSingleton<HistoryService>(service);
    return service;
  }

  Future<History?> getHistoryByUniqueKey(String uniqueKey) async {
    // 从数据库查询
    final history = await storage.getHistoryByUniqueKey(uniqueKey);
    return history;
  }

  Future<List<History>> getAllHistories() async {
    return await storage.getHistories();
  }

  Future<List<History>> getHistoriesWithPagination(
    int page,
    int pageSize,
  ) async {
    final offset = page * pageSize;
    return await storage.getHistoriesWithPagination(pageSize, offset);
  }

  Future<void> clearAllHistories() async {
    await storage.clearAllHistories();
  }

  Future<void> deleteHistory(int id) async {
    await storage.deleteHistory(id);
  }

  /// 开始记录播放历史
  Future<void> addHistory({
    required String url,
    required String headers,
    required Duration duration,
    required HistoriesType type,
    int? mediaLibraryId,
  }) async {
    // 生成唯一键
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(url);

    // 检查是否已存在历史记录
    final existingHistory = await getHistoryByUniqueKey(uniqueKey);

    if (existingHistory == null) {
      // 创建新的历史记录
      final history = HistoriesCompanion.insert(
        uniqueKey: uniqueKey,
        duration: duration.inMilliseconds,
        position: 0,
        url: Value(url),
        headers: Value(headers),
        type: type,
        mediaLibraryId: Value(mediaLibraryId),
        updateTime: DateTime.now().millisecondsSinceEpoch,
      );
      await storage.createHistory(history);
    } else {
      // 更新历史记录
      final companion = HistoriesCompanion(
        id: Value(existingHistory.id),
        headers: Value(headers),
        duration: Value(duration.inMilliseconds),
        updateTime: Value(DateTime.now().millisecondsSinceEpoch),
      );
      await storage.updateHistory(companion);
    }
  }

  /// 更新播放进度
  Future<void> updateProgress({
    required Duration position,
    required Duration duration,
    required int id,
  }) async {
    final companion = HistoriesCompanion(
      id: Value(id),
      position: Value(position.inMilliseconds),
      duration: Value(duration.inMilliseconds),
      updateTime: Value(DateTime.now().millisecondsSinceEpoch),
    );

    await storage.updateProgress(companion);
  }

  /// 获取播放历史
  Future<History?> getPlaybackHistory(String videoPath) async {
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
    return await getHistoryByUniqueKey(uniqueKey);
  }

  /// 获取播放进度百分比
  Future<double> getProgressPercentage(String videoPath) async {
    final history = await getPlaybackHistory(videoPath);
    if (history == null || history.duration <= 0) {
      return 0.0;
    }
    return history.position / history.duration;
  }

  /// TODO 判断视频是否已观看完成
  // Future<bool> isVideoCompleted(String videoPath) async {
  //   final progressPercentage = await getProgressPercentage(videoPath);
  //   return progressPercentage >= 0.9; // 观看90%以上认为已完成
  // }
}
