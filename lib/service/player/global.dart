import 'package:dandanplay_flutter/service/storage.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:signals_flutter/signals_flutter.dart';

class GlobalPlayerService {
  // 视频文件的真实地址
  String currentVideoPath = '';
  // 虚拟路径(1/video.mp4)
  String virtualVideoPath = '';
  Map<String, String> headers = {};
  HistoriesType historiesType = HistoriesType.local;
  int? mediaLibraryId;
  Signal<int> currentIndex = Signal(0);
  bool fromHistory = false;
  late BuildContext notificationContext;

  static Future<void> register() async {
    final service = GlobalPlayerService();
    GetIt.I.registerSingleton<GlobalPlayerService>(service);
  }

  void showNotification(String message) {
    showRawFToast(
      context: notificationContext,
      alignment: FToastAlignment.bottomLeft,
      duration: Duration(seconds: 3),
      builder: (context, entry) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          decoration: BoxDecoration(
            color: Colors.grey.shade900.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(message, style: TextStyle(color: Colors.white)),
        );
      },
    );
  }

  String getVideoName() {
    return currentVideoPath.split('/').last.split('.').first;
  }

  bool setMediaLibraryIdFromPath() {
    final parts = virtualVideoPath.split('/');
    if (parts.length < 2) {
      mediaLibraryId = null;
      return false;
    }
    mediaLibraryId = int.tryParse(parts[0]);
    if (mediaLibraryId == null) {
      return false;
    }
    return true;
  }
}
