import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dandanplay_flutter/service/configure.dart';
import 'package:dandanplay_flutter/service/history.dart';
import 'package:dandanplay_flutter/service/player/danmaku.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/utils/format.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:image/image.dart' as img;
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:path_provider/path_provider.dart';
import 'package:signals_flutter/signals_flutter.dart';

class UpdateTimer {
  int _time = 1000;
  int _longTime = 10000;
  Function(Timer timer) updateFn;
  late Timer _timer;
  UpdateTimer(this.updateFn, {int time = 1000, int longTime = 10000}) {
    _time = time;
    _longTime = longTime;
  }
  void init() {
    _timer = Timer.periodic(Duration(milliseconds: _time), updateFn);
  }

  void changeTime({bool isLong = false}) {
    _timer.cancel();
    int time = isLong ? _longTime : _time;
    _timer = Timer.periodic(Duration(milliseconds: time), updateFn);
  }

  void dispose() {
    _timer.cancel();
  }
}

enum TimerType {
  // 历史记录
  history,
  // 弹幕状态更新
  danmaku,
}

/// 播放器状态枚举
enum PlayerState {
  /// 加载中
  loading,

  /// 准备播放
  ready,

  /// 播放中
  playing,

  /// 暂停
  paused,

  /// 缓冲中
  buffering,

  /// 错误状态
  error,
}

/// 轨道信息模型
class TrackInfo {
  final int index;
  final String id;
  final String language;
  final String title;

  const TrackInfo({
    required this.index,
    required this.id,
    required this.language,
    required this.title,
  });
}

/// 轨道类型工具类
class TrackUtils {
  /// 支持的字幕文件扩展名
  static const supportedSubtitleExtensions = {
    'srt',
    'ass',
    'ssa',
    'vtt',
    'sub',
    'idx',
    'sup',
  };

  /// 检查文件是否为支持的字幕格式
  static bool isSupportedSubtitleFile(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;
    return supportedSubtitleExtensions.contains(extension);
  }
}

class VideoPlayerService {
  late final Player _player;
  late final VideoController _videoController;
  final _subscriptions = <StreamSubscription>[];

  final _historyService = GetIt.I<HistoryService>();
  final _globalPlayerService = GetIt.I<GlobalPlayerService>();
  final _configureService = GetIt.I<ConfigureService>();

  // 播放器状态信号
  final Signal<PlayerState> _playerState = Signal(PlayerState.loading);
  final Signal<Duration> _position = Signal(Duration.zero);
  final Signal<Duration> _bufferedPosition = Signal(Duration.zero);
  final Signal<double> _playbackSpeed = Signal(1.0);
  final Signal<String?> _errorMessage = Signal(null);
  final Signal<int> _bitRate = Signal(0);
  final Signal<String> _videoName = Signal('');

  // 轨道状态信号
  final Signal<List<TrackInfo>> _audioTracks = Signal([]);
  final Signal<List<TrackInfo>> _subtitleTracks = Signal([]);
  final Signal<TrackInfo?> _externalSubtitle = signal(null);
  final Signal<int> _activeAudioTrack = Signal(-1);
  final Signal<int> _activeSubtitleTrack = Signal(-1);
  late String _currentVideoPath;
  late HistoriesType _historiesType;
  late int? _mediaLibraryId;
  late Map<String, String> _headers;
  Duration duration = Duration();
  final DanmakuService danmakuService = DanmakuService();
  late History _history;

  // 定时器组
  late final Map<TimerType, UpdateTimer> _timerGroup = {
    TimerType.history: UpdateTimer((_) => updatePlaybackHistory()),
    TimerType.danmaku: UpdateTimer(
      (_) => danmakuService.updatePlayPosition(_position.value),
    ),
  };

  VideoPlayerService() {
    _player = Player(
      configuration: PlayerConfiguration(
        bufferSize:
            _configureService.lowMemoryMode.value
                ? 15 * 1024 * 1024
                : 1500 * 1024 * 1024,
      ),
    );
    _videoController = VideoController(
      _player,
      configuration: VideoControllerConfiguration(
        enableHardwareAcceleration:
            _configureService.hardwareDecoderEnable.value,
        hwdec: _configureService.hardwareDecoder.value,
      ),
    );
    _currentVideoPath = _globalPlayerService.currentVideoPath;
    _historiesType = _globalPlayerService.historiesType;
    _mediaLibraryId = _globalPlayerService.mediaLibraryId;
    _headers = _globalPlayerService.headers;
    _videoName.value = _currentVideoPath.split('/').last;
  }

  /// 播放器状态
  ReadonlySignal<PlayerState> get playerState => _playerState.readonly();

  /// 当前播放位置
  ReadonlySignal<Duration> get position => _position.readonly();

  /// 已缓冲位置
  ReadonlySignal<Duration> get bufferedPosition => _bufferedPosition.readonly();

  /// 播放速度
  ReadonlySignal<double> get playbackSpeed => _playbackSpeed.readonly();

  /// 错误信息
  ReadonlySignal<String?> get errorMessage => _errorMessage.readonly();
  ReadonlySignal<int> get bitRate => _bitRate.readonly();
  ReadonlySignal<String> get videoName => _videoName.readonly();

  /// 音频轨道列表
  ReadonlySignal<List<TrackInfo>> get audioTracks => _audioTracks.readonly();

  /// 字幕轨道列表
  ReadonlySignal<List<TrackInfo>> get subtitleTracks =>
      _subtitleTracks.readonly();

  /// 当前活动音频轨道索引
  ReadonlySignal<int> get activeAudioTrack => _activeAudioTrack.readonly();

  /// 当前活动字幕轨道索引
  ReadonlySignal<int> get activeSubtitleTrack =>
      _activeSubtitleTrack.readonly();

  /// 视频播放器控制器
  VideoController get controller => _videoController;

  /// 初始化视频播放器
  Future<void> initialize() async {
    try {
      _setProperty();
      _playerState.value = PlayerState.loading;
      _errorMessage.value = null;

      final playable = Media(_currentVideoPath, httpHeaders: _headers);
      await _player.open(playable, play: false);

      duration = await _player.stream.duration.firstWhere(
        (d) => d != Duration.zero,
      );
      setPlaybackSpeed(_configureService.defaultPlaySpeed.value);
      _playerState.value = PlayerState.ready;

      await _historyService.addHistory(
        url: _globalPlayerService.virtualVideoPath,
        headers: jsonEncode(_headers),
        duration: duration,
        type: _historiesType,
        mediaLibraryId: _mediaLibraryId,
      );
      _history =
          (await _historyService.getPlaybackHistory(
            _globalPlayerService.virtualVideoPath,
          ))!;

      danmakuService.virtualVideoPath = _globalPlayerService.virtualVideoPath;
      danmakuService.history = _history;
      danmakuService.duration = duration;

      await _restoreProgress();

      danmakuService.loadDanmaku();

      await _loadTracks();

      _timerGroup.forEach((_, value) => value.init());

      _subscriptions.addAll([
        _player.stream.playing.listen(_onPlayingStateChanged),
        _player.stream.completed.listen(_onCompleted),
        _player.stream.buffering.listen(_onBufferingStateChanged),
        _player.stream.position.listen((p) => _position.value = p),
        _player.stream.buffer.listen((b) => _bufferedPosition.value = b),
        _player.stream.rate.listen((r) => _playbackSpeed.value = r),
        _player.stream.error.listen((e) {
          _playerState.value = PlayerState.error;
          _errorMessage.value = e;
          debugPrint('VideoPlayerService: Player error: $e');
        }),
        _player.stream.audioBitrate.listen((b) {
          if (b != null) _bitRate.value = b.toInt();
        }),
      ]);

      play();
    } catch (e) {
      _playerState.value = PlayerState.error;
      _errorMessage.value = e.toString();
      rethrow;
    }
  }

  Future<void> _setProperty() async {
    // var pp = _player.platform as NativePlayer;
    // if (Platform.isAndroid) {
    //   await pp.setProperty("ao", "audiotrack");
    // }
  }

  /// 播放视频
  Future<void> play() async {
    await _player.play();
  }

  /// 暂停视频
  Future<void> pause() async {
    await _player.pause();
  }

  /// 跳转到指定位置
  Future<void> seekTo(Duration position) async {
    _position.value = position;
    danmakuService.clear();
    danmakuService.resetDanmakuPosition();
    await _player.seek(position);
  }

  /// 相对跳转
  void seekRelative(Duration offset) {
    final currentPosition = position.value;
    final newPosition = currentPosition + offset;
    seekTo(newPosition);
    final offsetText =
        offset.isNegative
            ? '-${formatDuration(-offset)}'
            : '+${formatDuration(offset)}';
    _globalPlayerService.showNotification('跳转 $offsetText');
  }

  /// 设置播放速度
  Future<void> setPlaybackSpeed(double speed) async {
    await _player.setRate(speed);
    _playbackSpeed.value = speed;
  }

  Future<void> doubleSpeed(bool isDouble) async {
    final currentSpeed = _playbackSpeed.value;
    final newSpeed = currentSpeed * (isDouble ? 2 : 0.5);
    await setPlaybackSpeed(newSpeed);
  }

  /// 切换播放/暂停
  Future<void> togglePlayPause() async {
    if (_playerState.value == PlayerState.playing) {
      await pause();
    } else if (_playerState.value == PlayerState.paused) {
      await play();
      danmakuService.syncWithVideo(true);
    }
  }

  void _onPlayingStateChanged(bool isPlaying) {
    if (_player.state.buffering) return; // Ignore if buffering
    if (isPlaying) {
      _playerState.value = PlayerState.playing;
      danmakuService.syncWithVideo(true);
      _timerGroup.forEach((_, value) => value.changeTime());
    } else {
      _playerState.value = PlayerState.paused;
      danmakuService.syncWithVideo(false);
      _timerGroup.forEach((_, value) => value.changeTime(isLong: true));
    }
  }

  void _onCompleted(bool isCompleted) {
    if (isCompleted) {
      _playerState.value = PlayerState.paused;
      danmakuService.syncWithVideo(false);
    }
  }

  void _onBufferingStateChanged(bool isBuffering) {
    if (isBuffering) {
      _playerState.value = PlayerState.buffering;
      danmakuService.syncWithVideo(false);
    } else {
      _playerState.value =
          _player.state.playing ? PlayerState.playing : PlayerState.paused;
      danmakuService.syncWithVideo(_player.state.playing);
    }
  }

  /// 更新播放历史记录
  Future<void> updatePlaybackHistory() async {
    await _historyService.updateProgress(
      id: _history.id,
      position: _position.value,
      duration: duration,
    );
  }

  Future<void> saveSnapshot() async {
    try {
      if (_history.uniqueKey.isEmpty) {
        debugPrint('VideoPlayerService: Cannot get video unique key');
        return;
      }

      final rawSnapshot = await _player.screenshot();
      if (rawSnapshot == null) {
        debugPrint('VideoPlayerService: Failed to take snapshot');
        return;
      }

      // The original code resized to a thumbnail of 300px width.
      final image = img.decodeImage(rawSnapshot);
      if (image == null) {
        debugPrint('VideoPlayerService: Failed to decode snapshot image');
        return;
      }

      final thumbnail = img.copyResize(image, width: 300);
      final imageBytes = img.encodeJpg(thumbnail);

      final documentsDir = await getApplicationDocumentsDirectory();

      final dir = Directory('${documentsDir.path}/screenshots');
      await dir.create(recursive: true);
      final file = File('${dir.path}/${_history.uniqueKey}');
      await file.writeAsBytes(imageBytes);
    } catch (e) {
      debugPrint('VideoPlayerService: 快照保存异常: $e');
      return;
    }
  }

  /// 恢复播放进度
  Future<Duration> restoreProgress() async {
    if (_history.position > 0) {
      final position = Duration(milliseconds: _history.position);
      await seekTo(position);
      return position;
    }
    return Duration.zero;
  }

  Future<void> _restoreProgress() async {
    try {
      if (_history.position > 0) {
        final position = Duration(milliseconds: _history.position);
        await seekTo(position);
        final positionText = formatDuration(position);
        _globalPlayerService.showNotification('已恢复到 $positionText');
        debugPrint('恢复播放历史: $positionText');
      }
    } catch (e) {
      debugPrint('恢复播放历史失败: $e');
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    try {
      await updatePlaybackHistory();
      await saveSnapshot();
    } catch (e) {
      debugPrint('VideoPlayerService: 保存最终历史记录失败: $e');
    }
    _timerGroup.forEach((_, value) => value.dispose());
    for (final s in _subscriptions) {
      s.cancel();
    }
    _subscriptions.clear();
    await _player.dispose();

    _playerState.dispose();
    _position.dispose();
    _bufferedPosition.dispose();
    _playbackSpeed.dispose();
    _bitRate.dispose();
    _videoName.dispose();
    _errorMessage.dispose();

    _audioTracks.dispose();
    _subtitleTracks.dispose();
    _externalSubtitle.dispose();
    _activeAudioTrack.dispose();
    _activeSubtitleTrack.dispose();

    danmakuService.dispose();
  }

  /// 加载所有轨道信息
  Future<void> _loadTracks() async {
    try {
      await loadAudioTracks();
      await loadSubtitleTracks();
    } catch (e) {
      debugPrint('VideoPlayerService: 加载轨道信息失败 - $e');
    }
  }

  /// 获取音频轨道信息
  Future<void> loadAudioTracks() async {
    try {
      final audios = _player.state.tracks.audio;
      final tracks = <TrackInfo>[];
      for (var i = 0; i < audios.length; i++) {
        final audio = audios[i];
        tracks.add(
          TrackInfo(
            index: i,
            id: audio.id,
            language: audio.language ?? '',
            title: audio.title ?? '音频-${audio.id}',
          ),
        );
      }
      _audioTracks.value = tracks;
      debugPrint('VideoPlayerService: 加载了 ${tracks.length} 个音频轨道');

      // Set default active track
      final activeTrack = _player.state.track.audio;
      final activeIndex = audios.indexWhere((t) => t.id == activeTrack.id);
      _activeAudioTrack.value = activeIndex;
    } catch (e) {
      debugPrint('VideoPlayerService: 获取音频轨道信息失败 - $e');
      _audioTracks.value = [];
    }
  }

  /// 设置活动音频轨道
  Future<void> setActiveAudioTrack(int trackIndex) async {
    final tracks = _audioTracks.value;
    if (trackIndex < 0 || trackIndex >= tracks.length) {
      throw Exception('无效的音频轨道索引: $trackIndex');
    }

    try {
      await _player.setAudioTrack(_player.state.tracks.audio[trackIndex]);
      _activeAudioTrack.value = trackIndex;
      final track = tracks[trackIndex];
      debugPrint('VideoPlayerService: 切换音频轨道成功 - ${track.title}');
    } catch (e) {
      debugPrint('VideoPlayerService: 切换音频轨道失败 - $e');
      rethrow;
    }
  }

  /// 获取字幕轨道信息
  Future<void> loadSubtitleTracks() async {
    try {
      final subtitles = _player.state.tracks.subtitle;
      var tracks = <TrackInfo>[];
      for (var i = 0; i < subtitles.length; i++) {
        final sub = subtitles[i];
        tracks.add(
          TrackInfo(
            index: i,
            id: sub.id,
            language: sub.language ?? '',
            title: sub.title ?? '字幕-$sub.id',
          ),
        );
      }
      _subtitleTracks.value = tracks;
      debugPrint('VideoPlayerService: 加载了 ${tracks.length} 个字幕轨道');

      final activeTrack = _player.state.track.subtitle;
      final activeIndex = subtitles.indexWhere((t) => t.id == activeTrack.id);
      _activeSubtitleTrack.value = activeIndex;
    } catch (e) {
      debugPrint('VideoPlayerService: 获取字幕轨道信息失败 - $e');
      _subtitleTracks.value = [];
    }
  }

  /// 设置活动字幕轨道
  Future<void> setActiveSubtitleTrack(int trackIndex) async {
    try {
      final tracks = _subtitleTracks.value;
      if (trackIndex < 0 || trackIndex >= tracks.length) {
        throw Exception('无效的字幕轨道索引: $trackIndex');
      }
      await _player.setSubtitleTrack(_player.state.tracks.subtitle[trackIndex]);
      _activeSubtitleTrack.value = trackIndex;
      final track = tracks[trackIndex];
      debugPrint('VideoPlayerService: 切换字幕轨道成功 - ${track.title}');
    } catch (e) {
      debugPrint('VideoPlayerService: 切换字幕轨道失败 - $e');
      rethrow;
    }
  }

  /// 加载外部字幕
  Future<void> loadExternalSubtitle(String filePath) async {
    try {
      await _player.setSubtitleTrack(SubtitleTrack.uri(filePath));
      final fileName = filePath.split('/').last;
      final externalTrack = TrackInfo(
        index: -1,
        id: 'external',
        language: '',
        title: fileName,
      );
      _externalSubtitle.value = externalTrack;
      _activeSubtitleTrack.value = _subtitleTracks.value.length;
      _subtitleTracks.value = [..._subtitleTracks.value, externalTrack];

      debugPrint('VideoPlayerService: 加载外部字幕成功 - $fileName');
    } catch (e) {
      debugPrint('VideoPlayerService: 加载外部字幕失败 - $e');
      rethrow;
    }
  }

  /// 移除外部字幕
  Future<void> removeExternalSubtitle() async {
    await setActiveSubtitleTrack(-1);
    _externalSubtitle.value = null;
    _subtitleTracks.value =
        _subtitleTracks.value.where((t) => t.index != -1).toList();
  }
}
