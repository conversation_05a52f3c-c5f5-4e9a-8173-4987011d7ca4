import 'dart:async';
import 'dart:io';

import 'package:canvas_danmaku/danmaku_controller.dart';
import 'package:canvas_danmaku/models/danmaku_content_item.dart';
import 'package:dandanplay_flutter/model/danmaku.dart';
import 'package:dandanplay_flutter/service/configure.dart';
import 'package:dandanplay_flutter/service/player/danmaku_optimizer.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/utils/crypto_utils.dart';
import 'package:dandanplay_flutter/utils/danmaku_api_utils.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:path_provider/path_provider.dart';
import 'package:signals_flutter/signals_flutter.dart';

class DanmakuService {
  late DanmakuController controller;

  ConfigureService configureService = GetIt.I.get<ConfigureService>();
  GlobalPlayerService globalPlayerService = GetIt.I.get<GlobalPlayerService>();
  // 弹幕相关信号
  List<Danmaku> _danmakus = [];
  final Signal<DanmakuSettings> _danmakuSettings = Signal(DanmakuSettings());
  final Signal<bool> danmakuEnabled = Signal(true);
  late String virtualVideoPath = globalPlayerService.virtualVideoPath;
  late History history;
  late Duration duration;
  int episodeId = 0;
  int animeId = 0;

  // 当前弹幕索引，用于跟踪已显示的弹幕位置
  int _currentDanmakuIndex = 0;

  /// 弹幕设置
  ReadonlySignal<DanmakuSettings> get danmakuSettings =>
      _danmakuSettings.readonly();

  Future<void> init() async {
    final sittings = await configureService.getDanmakuSettings();
    _danmakuSettings.value = sittings;
    controller.updateOption(sittings.toDanmakuOption());
  }

  void syncWithVideo(bool isPlaying) {
    if (isPlaying) {
      controller.resume();
    } else {
      controller.pause();
    }
  }

  void clear() {
    controller.clear();
    _currentDanmakuIndex = 0;
  }

  /// 重置弹幕位置，用于seek操作后重新开始弹幕显示
  void resetDanmakuPosition() {
    _currentDanmakuIndex = 0;
  }

  /// 根据当前播放位置更新弹幕显示
  void updatePlayPosition(Duration position) {
    if (_danmakus.isEmpty) return;

    // 从当前索引开始检查需要显示的弹幕
    while (_currentDanmakuIndex < _danmakus.length) {
      final danmaku = _danmakus[_currentDanmakuIndex];

      // 如果弹幕时间还没到，停止检查
      if (danmaku.time.inSeconds > position.inSeconds) {
        break;
      }
      _currentDanmakuIndex++;
      // 跳过显示过的弹幕
      if (danmaku.time.inSeconds < position.inSeconds) {
        continue;
      }
      // 数据源筛选
      switch (danmaku.source) {
        case 'BiliBili':
          if (!_danmakuSettings.value.bilibiliSource) continue;
          break;
        case 'Gamer':
          if (!_danmakuSettings.value.gamerSource) continue;
          break;
        case 'DanDanPlay':
          if (!_danmakuSettings.value.dandanSource) continue;
          break;
        default:
          if (!_danmakuSettings.value.otherSource) continue;
      }
      // 添加弹幕到显示器
      Future.delayed(
        Duration(
          milliseconds: danmaku.time.inMilliseconds - position.inSeconds * 1000,
        ),
        () => _addDanmakuToController(danmaku),
      );
    }
  }

  /// 将弹幕添加到控制器中显示
  void _addDanmakuToController(Danmaku danmaku) {
    try {
      // 根据弹幕类型转换为canvas_danmaku的类型
      DanmakuItemType danmakuType;
      switch (danmaku.type) {
        case 1:
          danmakuType = DanmakuItemType.scroll; // 滚动弹幕
          break;
        case 4:
          danmakuType = DanmakuItemType.bottom; // 底部弹幕
          break;
        case 5:
          danmakuType = DanmakuItemType.top; // 顶部弹幕
          break;
        default:
          danmakuType = DanmakuItemType.scroll; // 默认滚动弹幕
      }

      // 调用controller的addDanmaku方法
      controller.addDanmaku(
        DanmakuContentItem(
          danmaku.text,
          type: danmakuType,
          color: danmaku.color,
        ),
      );
    } catch (e) {
      debugPrint('添加弹幕失败: $e');
    }
  }

  /// 加载弹幕
  Future<void> loadDanmaku({bool force = false}) async {
    try {
      // 1. 检查本地缓存 (非强制刷新时)
      if (!force) {
        final cachedDanmakus = await _getCachedDanmakus(virtualVideoPath);
        if (cachedDanmakus.isNotEmpty) {
          // 按时间排序弹幕
          cachedDanmakus.sort((a, b) => a.time.compareTo(b.time));
          _danmakus = cachedDanmakus;
          _currentDanmakuIndex = 0; // 重置弹幕索引
          controller.clear();
          globalPlayerService.showNotification(
            '从缓存加载弹幕: ${cachedDanmakus.length}条',
          );
          return;
        }
      }
      final comments = await DanmakuApiUtils.getComments(episodeId);
      final danmakus = comments.map((comment) => comment.toDanmaku()).toList();

      danmakus.sort((a, b) => a.time.compareTo(b.time));
      // 3. 保存到本地缓存
      if (danmakus.isNotEmpty) {
        await _saveDanmakus(virtualVideoPath, danmakus, episodeId, animeId);
      }
      _danmakus = danmakus;
      _currentDanmakuIndex = 0; // 重置弹幕索引
      controller.clear();
      globalPlayerService.showNotification('从API加载弹幕: ${danmakus.length}条');
    } catch (e) {
      debugPrint('加载弹幕失败: $e');
      // 加载失败时设置空列表，避免界面异常
      _danmakus = [];
    }
  }

  /// 从缓存获取弹幕数据
  Future<List<Danmaku>> _getCachedDanmakus(String videoPath) async {
    try {
      final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
      final documentsDir = await getApplicationDocumentsDirectory();
      final cacheDir = Directory('${documentsDir.path}/danmaku');
      final danmakuFile = File('${cacheDir.path}/$uniqueKey.json');

      if (!await danmakuFile.exists()) {
        return [];
      }

      // 读取并解析弹幕文件
      final jsonString = await danmakuFile.readAsString();
      final danmakuData = DanmakuFile.fromJsonString(jsonString);

      // 检查过期时间
      final expireTime = danmakuData.expireTime.millisecondsSinceEpoch;
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now > expireTime) {
        debugPrint('弹幕缓存已过期，删除缓存文件');
        await danmakuFile.delete();
        return [];
      }
      episodeId = danmakuData.episodeId;
      animeId = danmakuData.animeId;
      // 解析弹幕数据
      return danmakuData.danmakus;
    } catch (e) {
      debugPrint('读取缓存弹幕失败: $e');
      return [];
    }
  }

  /// 带重试机制的弹幕获取
  Future<List<Danmaku>> _fetchDanmakusWithRetry(String videoPath) async {
    const maxRetries = 3;
    const retryDelay = Duration(seconds: 2);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint('尝试获取弹幕，第$attempt次');
        return await _fetchDanmakusFromApi(videoPath);
      } catch (e) {
        debugPrint('第$attempt次获取弹幕失败: $e');

        // 检查是否为网络错误
        if (_isNetworkError(e)) {
          debugPrint('检测到网络错误，将重试');
        } else {
          debugPrint('非网络错误，停止重试');
          break; // 非网络错误，不重试
        }

        if (attempt == maxRetries) {
          rethrow; // 最后一次尝试失败，抛出异常
        }

        // 等待后重试
        await Future.delayed(retryDelay);
      }
    }

    return []; // 重试失败或非网络错误
  }

  /// 检查是否为网络错误
  bool _isNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('socket') ||
        errorString.contains('dns') ||
        errorString.contains('unreachable');
  }

  /// 从弹弹play API获取弹幕数据
  Future<List<Danmaku>> _fetchDanmakusFromApi(String videoPath) async {
    try {
      // 计算视频文件信息
      final videoInfo = await _getVideoInfo(videoPath);
      if (videoInfo == null) {
        debugPrint('无法获取视频信息');
        return [];
      }
      // 调用弹弹play API匹配视频
      final episodes = await DanmakuApiUtils.matchVideo(
        fileName: videoInfo['fileName'],
        fileHash: videoInfo['fileHash'],
        fileSize: videoInfo['fileSize'],
        duration: videoInfo['duration'],
      );

      if (episodes.isEmpty) {
        debugPrint('未找到匹配的节目');
        return [];
      }

      // 获取第一个匹配结果的弹幕
      final episode = episodes.first;
      episodeId = episode.episodeId;
      animeId = episode.animeId;
      final comments = await DanmakuApiUtils.getComments(episode.episodeId);
      // 转换为内部弹幕格式
      return comments.map((comment) => comment.toDanmaku()).toList();
    } catch (e) {
      debugPrint('从API获取弹幕失败: $e');
      return [];
    }
  }

  /// 保存弹幕数据
  Future<void> _saveDanmakus(
    String videoPath,
    List<Danmaku> danmakus,
    int episodeId,
    int animeId,
  ) async {
    try {
      // 生成缓存文件路径
      final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
      final documentsDir = await getApplicationDocumentsDirectory();
      final cacheDir = Directory('${documentsDir.path}/danmaku');
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      final cacheFile = File('${cacheDir.path}/$uniqueKey.json');

      // 添加缓存元数据
      final cacheData = DanmakuFile(
        videoPath: videoPath,
        cacheTime: DateTime.now(),
        expireTime: DateTime.now().add(
          DanmakuOptimizer.getCacheExpiration(danmakuCount: danmakus.length),
        ),
        danmakus: danmakus,
        episodeId: episodeId,
        animeId: animeId,
      );

      await cacheFile.writeAsString(cacheData.toJsonString());

      debugPrint('弹幕缓存保存成功: ${cacheFile.path}, 弹幕数量: ${danmakus.length}');
    } catch (e) {
      debugPrint('保存弹幕缓存失败: $e');
    }
  }

  /// 获取视频文件信息（带进度状态）
  Future<Map<String, dynamic>?> _getVideoInfo(String videoPath) async {
    try {
      if (videoPath.startsWith('http://') || videoPath.startsWith('https://')) {
        // 网络视频：尝试获取真实文件hash
        try {
          // 使用异步网络hash计算，避免阻塞主线程
          final hashResult =
              await DanmakuOptimizer.calculateNetworkVideoHashAsync(
                videoPath,
                onProgress: (downloaded, total) {
                  debugPrint('下载进度: $downloaded / ${total ?? "未知"}');
                },
              );
          return {
            'fileName': Uri.parse(videoPath).pathSegments.last,
            'fileHash': hashResult.hash,
            'fileSize': hashResult.totalFileSize ?? 0,
            'duration': duration.inSeconds,
          };
        } catch (e) {
          debugPrint('网络视频hash计算失败，回退到URL hash: $e');
          // 回退到URL hash
          return {
            'fileName': Uri.parse(videoPath).pathSegments.last,
            'fileHash': CryptoUtils.calculateNetworkVideoHash(videoPath),
            'fileSize': 0,
            'duration': duration.inSeconds,
          };
        }
      } else {
        final file = File(videoPath);
        if (!await file.exists()) {
          return null;
        }

        final fileSize = await file.length();
        final fileHash = await DanmakuOptimizer.calculateVideoHashAsync(
          videoPath,
        );
        final fileName = file.path.split('/').last;

        return {
          'fileName': fileName,
          'fileHash': fileHash,
          'fileSize': fileSize,
          'duration': duration.inSeconds,
        };
      }
    } catch (e) {
      debugPrint('获取视频信息失败: $e');
      return null;
    }
  }

  /// 搜索番剧集数
  Future<List<Anime>> searchEpisodes(String animeName) async {
    try {
      final animes = await DanmakuApiUtils.searchEpisodes(animeName);

      return animes;
    } catch (e) {
      debugPrint('搜索番剧失败: $e');
      rethrow;
    }
  }

  /// 选择episodeId并加载弹幕
  Future<void> selectEpisodeAndLoadDanmaku(int animeId, int episodeId) async {
    try {
      // 获取弹幕
      final comments = await DanmakuApiUtils.getComments(episodeId);

      // 转换为内部弹幕格式并排序
      final danmakus = comments.map((comment) => comment.toDanmaku()).toList();
      danmakus.sort((a, b) => a.time.compareTo(b.time));

      // 保存到缓存
      if (danmakus.isNotEmpty) {
        await _saveDanmakus(virtualVideoPath, danmakus, episodeId, animeId);
      }

      // 更新弹幕列表
      _danmakus = danmakus;
      _currentDanmakuIndex = 0;
      debugPrint('手动选择弹幕加载成功: ${danmakus.length}条');
    } catch (e) {
      debugPrint('手动选择弹幕加载失败: $e');
      rethrow;
    }
  }

  /// 更新弹幕设置
  void updateDanmakuSettings(DanmakuSettings settings) {
    _danmakuSettings.value = settings;
    configureService.setDanmakuSettings(settings);
    controller.updateOption(settings.toDanmakuOption());
    debugPrint('弹幕设置已更新: $settings');
  }

  void dispose() {
    _danmakuSettings.dispose();
  }
}
