import 'dart:convert';

import 'package:dandanplay_flutter/model/file_item.dart';
import 'package:dandanplay_flutter/service/history.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/utils/crypto_utils.dart';
import 'package:get_it/get_it.dart';
import 'package:signals_flutter/signals_flutter.dart';
import 'package:string_util_xx/StringUtilxx.dart';
import 'package:webdav_client_plus/webdav_client_plus.dart';

abstract class FileExplorerProvider {
  Future<List<FileItem>> listFiles(String path, String rootPath);
  void dispose();
}

class FileExplorerService {
  final Signal<FileExplorerProvider?> provider = signal(null);
  final path = signal('/');
  final Signal<String?> error = signal(null);
  final _globalPlayerService = GetIt.I.get<GlobalPlayerService>();
  MediaLibrary? _mediaLibrary;

  late final FutureSignal<List<FileItem>> files = futureSignal(() async {
    if (provider.value == null || _mediaLibrary == null) {
      return [];
    }
    return provider.value!.listFiles(path.value, _mediaLibrary!.id.toString());
  }, dependencies: [path, provider]);

  static void register() {
    final service = FileExplorerService();
    GetIt.I.registerSingleton<FileExplorerService>(service);
  }

  void setProvider(
    FileExplorerProvider newProvider,
    MediaLibrary mediaLibrary,
  ) {
    provider.value = newProvider;
    _mediaLibrary = mediaLibrary;
    path.value = '/';
  }

  void goDir(String name) {
    path.value = '${path.value}$name/';
  }

  bool goBack() {
    if (path.value == '/') {
      return false;
    }
    path.value =
        '${path.value.split('/').sublist(0, path.value.split('/').length - 2).join('/')}/';
    return true;
  }

  void cd(String newPath) {
    path.value = newPath;
  }

  Future<bool> findVideo(String url) async {
    final parts = url.split('/');
    if (parts.length <= 2) {
      return false;
    }
    // 去掉第一个和最后一个部分
    final middleParts = parts.sublist(1, parts.length - 1);
    cd('/${middleParts.join('/')}/');
    final list = await files.future;
    final fileName = url.split('/').last;
    for (int i = 0; i < list.length; i++) {
      if (list[i].name == fileName) {
        changeVideo(i, list[i].path);
        return true;
      }
    }
    return false;
  }

  String? switchVideo(int index) {
    final asyncList = files.value;
    if (asyncList.hasValue) {
      final list = asyncList.requireValue;
      if (index >= list.length || index < 0 || list[index].isFolder) {
        return null;
      }
      return _mediaLibrary!.url + list[index].path;
    }
    return null;
  }

  Future<void> refresh() async {
    await files.refresh();
  }

  Future<void> selectVideo(int index) async {
    final asyncList = files.value;
    if (asyncList.hasValue) {
      final list = asyncList.requireValue;
      if (index >= list.length || index < 0 || list[index].isFolder) {
        return;
      }
      changeVideo(index, list[index].path);
    }
  }

  Future<void> changeVideo(int index, String path) async {
    final videoPath = '${_mediaLibrary!.url}$path';
    _globalPlayerService.currentVideoPath = videoPath;
    final headers = jsonDecode(_mediaLibrary!.headers) as Map<String, dynamic>;
    _globalPlayerService.headers['Authorization'] = headers['Authorization'];
    _globalPlayerService.currentIndex.value = index;
    _globalPlayerService.historiesType = HistoriesType.mediaLibrary;
    _globalPlayerService.fromHistory = false;
    _globalPlayerService.virtualVideoPath = '${_mediaLibrary!.id}$path';
  }
}

// WebDAV implementation (placeholder)
class WebDAVFileExplorerProvider implements FileExplorerProvider {
  WebdavClient? client;

  WebDAVFileExplorerProvider(MediaLibrary mediaLibrary) {
    client = null;
    if (mediaLibrary.isAnonymous) {
      client = WebdavClient.noAuth(url: mediaLibrary.url);
    } else {
      client = WebdavClient.basicAuth(
        url: mediaLibrary.url,
        user: mediaLibrary.account!,
        pwd: mediaLibrary.password!,
      );
    }
  }

  @override
  Future<List<FileItem>> listFiles(String path, String rootPath) async {
    if (client == null) {
      return [];
    }
    List<FileItem> list = [];
    var fileList = await client!.readDir(path);
    for (var file in fileList) {
      final filePath = '$path${file.name}';
      if (FileItem.getFileType(file.name) != FileType.video && !file.isDir) {
        continue;
      }
      if (file.isDir) {
        list.add(
          FileItem(name: file.name, path: filePath, type: FileType.folder),
        );
        continue;
      }
      var uniqueKey = CryptoUtils.generateVideoUniqueKey('$rootPath$filePath');
      var history = await GetIt.I.get<HistoryService>().getHistoryByUniqueKey(
        uniqueKey,
      );
      list.add(
        FileItem(
          name: file.name,
          path: filePath,
          type: FileItem.getFileType(file.name),
          size: file.size,
          uniqueKey: uniqueKey,
          history: history,
        ),
      );
    }
    // 根据文件名排序，同时文件夹放在最前面
    list.sort((a, b) {
      if (a.isFolder && !b.isFolder) {
        return -1;
      }
      if (!a.isFolder && b.isFolder) {
        return 1;
      }
      return StringUtilxx_c.compareExtend(a.name, b.name);
    });
    return list;
  }

  @override
  void dispose() {}
}

// Local file system implementation (placeholder)
class LocalFileExplorerProvider implements FileExplorerProvider {
  @override
  Future<List<FileItem>> listFiles(String path, String rootPath) async {
    // TODO: Implement local file system listing
    return [
      FileItem(
        name: 'Local Folder',
        path: '$path/Local Folder',
        type: FileType.folder,
      ),
      FileItem(
        name: 'local_video.mp4',
        path: '$path/local_video.mp4',
        type: FileType.video,
        size: 1024 * 1024 * 200, // 200MB
      ),
    ];
  }

  @override
  void dispose() {}
}
