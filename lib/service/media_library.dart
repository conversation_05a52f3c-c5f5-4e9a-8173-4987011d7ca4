import 'dart:convert';

import 'package:dandanplay_flutter/service/storage.dart';
import 'package:drift/drift.dart';
import 'package:signals_flutter/signals_flutter.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';

final uuid = Uuid();

extension MediaLibraryExtension on MediaLibrary {
  static MediaLibrary create() {
    return MediaLibrary(
      id: -1,
      name: '',
      url: '',
      headers: '{}',
      mediaLibraryType: MediaLibraryType.webdav,
      isAnonymous: false,
    );
  }
}

class MediaLibraryService {
  StorageService storage;

  MediaLibraryService({required this.storage});
  // TODO 改为Future
  final Signal<List<MediaLibrary>> mediaLibraries = signal([]);

  static Future<void> register(StorageService ss) async {
    var service = MediaLibraryService(storage: ss);
    await service.init();
    GetIt.I.registerSingleton<MediaLibraryService>(service);
  }

  Future<void> init() async {
    mediaLibraries.value = await storage.getMediaLibraries();
  }

  Future<MediaLibrary?> getMediaLibrary(int id) async {
    return await storage.getMediaLibrary(id);
  }

  Future<void> updateMediaLibrary(MediaLibrary mediaLibrary) async {
    if (mediaLibrary.mediaLibraryType == MediaLibraryType.webdav) {
      if (mediaLibrary.isAnonymous) {
        mediaLibrary = mediaLibrary.copyWith(
          headers: jsonEncode({
            "Authorization": "Basic ${base64Encode(utf8.encode(':'))}",
          }),
        );
      } else {
        mediaLibrary = mediaLibrary.copyWith(
          headers: jsonEncode({
            "Authorization":
                "Basic ${base64Encode(utf8.encode('${mediaLibrary.account!}:${mediaLibrary.password!}'))}",
          }),
        );
      }
    }
    if (mediaLibrary.id == -1) {
      await storage.createMediaLibrary(
        MediaLibrariesCompanion.insert(
          name: mediaLibrary.name,
          url: mediaLibrary.url,
          headers: mediaLibrary.headers,
          mediaLibraryType: mediaLibrary.mediaLibraryType,
          isAnonymous: Value(mediaLibrary.isAnonymous),
          account: Value(mediaLibrary.account),
          password: Value(mediaLibrary.password),
        ),
      );
    } else {
      await storage.updateMediaLibrary(
        MediaLibrariesCompanion(
          id: Value(mediaLibrary.id),
          name: Value(mediaLibrary.name),
          url: Value(mediaLibrary.url),
          headers: Value(mediaLibrary.headers),
          mediaLibraryType: Value(mediaLibrary.mediaLibraryType),
          isAnonymous: Value(mediaLibrary.isAnonymous),
          account: Value(mediaLibrary.account),
          password: Value(mediaLibrary.password),
        ),
      );
    }
    init();
  }

  Future<void> deleteMediaLibrary(int id) async {
    await storage.deleteMediaLibrary(id);
    init();
  }
}
